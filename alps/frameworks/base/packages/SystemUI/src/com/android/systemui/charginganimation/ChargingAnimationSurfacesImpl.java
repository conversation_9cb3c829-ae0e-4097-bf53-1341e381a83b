package com.android.systemui.charginganimation;

import android.app.KeyguardManager;
import android.content.Context;
import android.graphics.PixelFormat;
import android.graphics.drawable.AnimationDrawable;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemProperties;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.view.Window;

import com.android.systemui.broadcast.BroadcastDispatcher;
import com.android.systemui.charginganimation.view.FrameAnimationView;
import com.android.systemui.charginganimation.view.HwChargingView;
import com.android.systemui.dagger.SysUISingleton;
import com.android.systemui.statusbar.notification.collection.NotificationEntry;
import com.android.systemui.statusbar.policy.HeadsUpManager;
import com.android.systemui.statusbar.policy.HeadsUpWhenExpandedListener;
import com.android.systemui.statusbar.notification.collection.NotifPipeline;

import java.util.ArrayList;
import java.util.List;

import com.android.systemui.res.R;

//redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 begin
import android.app.ActivityManager;
import android.app.ActivityTaskManager;
import android.os.RemoteException;
import android.app.TaskStackListener;
//redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 end

import javax.inject.Inject;
import com.android.systemui.charginganimation.ChargingReceiver.ChargingCallback;

/**
 * <AUTHOR>
 */
@SysUISingleton
public class ChargingAnimationSurfacesImpl extends ChargingAnimationSurfaces implements
        HeadsUpWhenExpandedListener,ChargingCallback {

    private final Context context;
    private WindowManager mWindowManager;
    private final DisplayMetrics mDisplayMetrics;
    private final HeadsUpManager mHeadsUpManager;
    private WindowManager.LayoutParams layoutParams;
    private final WindowManager.LayoutParams mLpChanged = new WindowManager.LayoutParams();
    private int mTop;

    private String TAG = "ChargingAnimationSurfacesImpl";


    private FrameAnimationView mAnimationView;

    View popupView;

    public static int screenState;

    private ChargingReceiver mChargingReceiver;
    private int mBatteryLevel = -1;

    //final ChargingAnimationSurfacesImpl.Receiver mReceiver = new ChargingAnimationSurfacesImpl.Receiver();

    @Inject
    public ChargingAnimationSurfacesImpl(
        Context context,
        WindowManager mWindowManager,
        DisplayMetrics mDisplayMetrics,
        HeadsUpManager mHeadsUpManager,
        BroadcastDispatcher broadcastDispatcher,
        NotifPipeline notifPipeline
    ) {
        this.context = context;
        this.mWindowManager = mWindowManager;
        this.mDisplayMetrics = mDisplayMetrics;
        this.mHeadsUpManager = mHeadsUpManager;
        mBroadcastDispatcher = broadcastDispatcher;
        mChargingReceiver = new ChargingReceiver(
                context,
                new Handler(Looper.getMainLooper()),
                broadcastDispatcher,
                this,
                notifPipeline
        );
    }

    @Override
    public void start() {
        //mReceiver.init();
        createAndAddWindows();
        mChargingReceiver.register();
    }
    //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 begin

    @Override
    public void onDestroy() {
        mChargingReceiver.unregister();
        unregisterTaskStackListener();
    }
    //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 end

    private Handler mChangeTipsHandler = new Handler(Looper.getMainLooper());
    private Runnable mChangeTipsRunnable;
    private int i;

    private void autoConfirm(int cnt, int time) {
        if (mChangeTipsRunnable != null) {
            mChangeTipsHandler.removeCallbacks(mChangeTipsRunnable);
        }
        i = cnt;
        mChangeTipsRunnable = new Runnable() {
            @Override
            public void run() {
                if (i >= 0) {
                    mChangeTipsHandler.postDelayed(this, time);
                    i--;
                } else {
                    removeChargeView();
                }
            }
        };
        mChangeTipsHandler.post(mChangeTipsRunnable);
    }

    private HwChargingView mHwChargingView;
    //int batteryLevel;

    private void createAndAddWindows() {

        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();
        popupView = LayoutInflater.from(context).inflate(R.layout.sr_lock_screen_charging,  null);
        mAnimationView = popupView.findViewById(R.id.animation_view);
        mHwChargingView = popupView.findViewById(R.id.hw_charging);

        popupView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);

        layoutParams = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                        WindowManager.LayoutParams.TYPE_KEYGUARD_DIALOG :
                        WindowManager.LayoutParams.TYPE_SYSTEM_ALERT,
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                        WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD |
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS |
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON |
                        WindowManager.LayoutParams.TYPE_DRAG |
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS |
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                        WindowManager.LayoutParams.FLAG_FULLSCREEN |
                         WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON,
                PixelFormat.TRANSLUCENT
        );
        layoutParams.token = new Binder();
        layoutParams.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
        layoutParams.packageName = context.getPackageName();
        layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS;

        layoutParams.y = mTop;
        mWindowManager.addView(popupView, layoutParams);
        mAnimationView.startAnimation();

        Log.d(TAG,"createAndAddWindows" + popupView + "mAnimationView= " + mAnimationView);
        popupView.setVisibility(View.GONE);

        popupView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                        removeChargeView();
                        yield true;
                    }
                    default -> false;
                };
            }
        });
    }
    //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 begin
    ActivityTaskManager mActivityTaskManager;
    private TaskStackListener mTaskStackListener;
    private void checkLockScreenState() {
        mActivityTaskManager = (ActivityTaskManager) context.getSystemService(Context.ACTIVITY_TASK_SERVICE);
        if (mActivityTaskManager != null) {
            mTaskStackListener = new TaskStackListener() {
                @Override
                public void onTaskMovedToFront(ActivityManager.RunningTaskInfo taskInfo) {
                    if (taskInfo.baseActivity != null) {
                        String packageName = taskInfo.baseActivity.getPackageName();
                        Log.d("longOnTaskMovedToFront", "packageName = " + packageName);
                        removeChargeView();
                    }
                    try {
                        super.onTaskMovedToFront(taskInfo);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
            };
            mActivityTaskManager.registerTaskStackListener(mTaskStackListener);
        }
    }

    public void removeChargeView(){
        if(popupView != null){
            popupView.setVisibility(View.GONE);
        }
        unregisterTaskStackListener();
    }

    private void unregisterTaskStackListener() {
        if (mActivityTaskManager != null && mTaskStackListener != null) {
            try {
                mActivityTaskManager.unregisterTaskStackListener(mTaskStackListener);
                mTaskStackListener = null;
            } catch (Exception e) {
                Log.e("TAG", "Failed to cancel the listener", e);
            }
        }
    }
    //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 end
    @Override
    public void onAlarmTrigger() {
        Log.d(TAG, "onAlarmTrigger");
        removeChargeView();
    }

    @Override
    public void onCallIncoming() {
        Log.d(TAG, "onCallIncoming");
        removeChargeView();
    }

    @Override
    public boolean shouldHeadsUpWhenExpanded(NotificationEntry entry) {
        throw new UnsupportedOperationException("Not yet implemented");
    }


    private final BroadcastDispatcher mBroadcastDispatcher;


    @Override
    public void onPowerConnected() {
        Log.d(TAG, "onPowerConnected");
        ChargingAnimationSurfacesImpl.screenState = mWindowManager.getDefaultDisplay().getState();
        boolean inKeyguardRestricted = ((KeyguardManager)context.getSystemService(
                Context.KEYGUARD_SERVICE)).inKeyguardRestrictedInputMode();

        if (inKeyguardRestricted && SystemProperties.getBoolean("ro.system.custom_unlock_charge_animation", true)) {
            new Handler(Looper.getMainLooper()).post(() -> {
                mHwChargingView.postInvalidate();
                popupView.setVisibility(View.VISIBLE);
                //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 begin
                checkLockScreenState();
                autoConfirm(10, 1000);
            });
        }
    }

    @Override
    public void onPowerDisconnected() {
        removeChargeView();
        unregisterTaskStackListener();
    }
    //redmine 342034 modify chargeAnimation display priority longshichun 2025/06/11 end
    @Override
    public void onBatteryLevelChanged(int level) {
        mBatteryLevel = level;
        if (mBatteryLevel != -1) {
            mHwChargingView.setProgress(mBatteryLevel);
        }else {
            mHwChargingView.setProgress(50);
        }
        if(mBatteryLevel == 100){
            mAnimationView.isCharged = true;
        }
        mHwChargingView.postInvalidate();
    }

}
