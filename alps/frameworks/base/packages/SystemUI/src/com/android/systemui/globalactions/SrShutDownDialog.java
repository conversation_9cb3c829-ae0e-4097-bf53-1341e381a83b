package com.android.systemui.globalactions;

import static android.view.Display.DEFAULT_DISPLAY;

import android.annotation.TargetApi;
import android.app.ActivityManager;
import android.app.Dialog;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
import android.app.Notification;
import android.content.BroadcastReceiver;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
import android.content.IntentFilter;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
import android.graphics.Bitmap;
import android.graphics.RenderEffect;
import com.android.internal.R;
import android.graphics.Shader;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.RemoteException;
import android.os.UserManager;
import android.telecom.TelecomManager;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
import android.telephony.TelephonyManager;
import android.util.Log;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.WindowManagerGlobal;
import android.view.animation.Animation;
import android.view.animation.BounceInterpolator;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.window.ScreenCapture;

import com.android.systemui.plugins.GlobalActions;
import com.android.systemui.settings.UserTracker;
import com.android.systemui.shade.ShadeController;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
import com.android.systemui.statusbar.notification.collection.NotifPipeline;
import com.android.systemui.statusbar.notification.collection.NotificationEntry;
import com.android.systemui.statusbar.notification.collection.notifcollection.NotifCollectionListener;
//Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
import com.android.systemui.util.EmergencyDialerConstants;

/*Redmine 316819 Shutdown interface UI by yangyang 2025.02.17*/
public class SrShutDownDialog extends Dialog implements View.OnClickListener,View.OnLongClickListener {

    private Context mContext;
    private String TAG = "SrShutDownDialog";
    private ImageView mPowerOffView;
    private ImageView mRestartView;
    private ImageView mEmergencyStarView;
    private TextView mPowerOffText;
    private TextView mRestartText;
    private TextView mEmergencyStarText;
    private final GlobalActions.GlobalActionsManager mWindowManagerFuncs;
    private final UserManager mUserManager;
    private LinearLayout mDialogWindow;
    private final ShadeController mShadeController;
    private final UserTracker mUserTracker;
    private final TelecomManager mTelecomManager;

    private final NotifPipeline mNotifPipeline;

    //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
    private final BroadcastReceiver mPhoneStateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TelephonyManager.ACTION_PHONE_STATE_CHANGED.equals(intent.getAction())) {
                String state = intent.getStringExtra(TelephonyManager.EXTRA_STATE);
                if (TelephonyManager.EXTRA_STATE_RINGING.equals(state) ||
                        TelephonyManager.EXTRA_STATE_OFFHOOK.equals(state)) {
                    Log.i(TAG, "Phone is ringing or in call, dismissing shutdown dialog");
                    dismiss();
                }
            }
        }
    };

    private final NotifCollectionListener mAlarmNotificationListener = new NotifCollectionListener() {
        @Override
        public void onEntryAdded(NotificationEntry entry) {
            if (Notification.CATEGORY_ALARM.equals(entry.getSbn().getNotification().category)) {
                dismiss();
            }
        }
    };
    //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end

    public SrShutDownDialog(Context context,
                            GlobalActions.GlobalActionsManager windowManagerFuncs,
                            TelecomManager telecomManager,
                            ShadeController shadeController,
                            UserTracker userTracker,
                            UserManager userManager,
                            NotifPipeline notifPipeline) {
        super(context, android.R.style.Theme_Material_NoActionBar_Fullscreen);
        mContext = context;
        mWindowManagerFuncs = windowManagerFuncs;
        mTelecomManager = telecomManager;
        mShadeController = shadeController;
        mUserTracker = userTracker;
        mUserManager = userManager;
        //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
        mNotifPipeline = notifPipeline;
        //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View decorView = getWindow().getDecorView();
        //Redmine 324012 Power Off Screen layout by yangyang 2025.03.19 start
        decorView.setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        getWindow().setBackgroundDrawable(new ColorDrawable(0xFF000000));
        //Redmine 324012 Power Off Screen layout by yangyang 2025.03.19 end
        setContentView(com.android.systemui.res.R.layout.sr_shutdown_dialog);
        initializeLayout();
        //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
        IntentFilter filter = new IntentFilter(TelephonyManager.ACTION_PHONE_STATE_CHANGED);
        mContext.registerReceiver(mPhoneStateReceiver, filter);
        mNotifPipeline.addCollectionListener(mAlarmNotificationListener);
        //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                mWindowManagerFuncs.onGlobalActionsHidden();
            }
        });
        setOnShowListener(new OnShowListener() {
            @Override
            public void onShow(DialogInterface dialog) {
                TranslateAnimation animIn = new TranslateAnimation(0, 0, 0, 0);
                animIn.setDuration(400);
                animIn.setInterpolator(new BounceInterpolator());
                animIn.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        mDialogWindow.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });
                mDialogWindow.startAnimation(animIn);
            }
        });
        mDialogWindow.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN){
                    if (!isTouchInsideView(mPowerOffView, event) && !isTouchInsideView(mRestartView, event) && !isTouchInsideView(mEmergencyStarView, event)) {
                        dismiss();
                    }
                }
                return false;
            }
        });

    }

    private void initializeLayout() {
        mPowerOffView = findViewById(com.android.systemui.res.R.id.iv_power_off);
        mRestartView = findViewById(com.android.systemui.res.R.id.iv_restart);
        mEmergencyStarView = findViewById(com.android.systemui.res.R.id.iv_emergency_star);
        mPowerOffText = findViewById(com.android.systemui.res.R.id.tv_power_off);
        mRestartText= findViewById(com.android.systemui.res.R.id.tv_restart);
        mEmergencyStarText = findViewById(com.android.systemui.res.R.id.tv_emergency_star);
        mDialogWindow = findViewById(com.android.systemui.res.R.id.sr_shutdown_dialog);
        mPowerOffText.setText(R.string.global_action_power_off);
        mRestartText.setText(R.string.global_action_restart);
        mEmergencyStarText.setText(R.string.global_action_emergency);
        Bitmap screenBitmap = takeScreenshot();
        mPowerOffView.setOnClickListener(this);
        mPowerOffView.setOnLongClickListener(this);
        mRestartView.setOnClickListener(this);
        mRestartView.setOnLongClickListener(this);
        mEmergencyStarView.setOnClickListener(this);
        mEmergencyStarView.setOnLongClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == com.android.systemui.res.R.id.iv_power_off){
            if (ActivityManager.isUserAMonkey()) {
                return;
            }
            // shutdown by making sure radio and power are handled accordingly.
            mWindowManagerFuncs.shutdown();
        }
        if (v.getId() == com.android.systemui.res.R.id.iv_emergency_star){
            if (mTelecomManager != null) {
                // Close shade so user sees the activity
                mShadeController.cancelExpansionAndCollapseShade();
                Intent intent = mTelecomManager.createLaunchEmergencyDialerIntent(
                        null /* number */);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK
                        | Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
                        | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intent.putExtra(EmergencyDialerConstants.EXTRA_ENTRY_TYPE,
                        EmergencyDialerConstants.ENTRY_TYPE_POWER_MENU);
                mContext.startActivityAsUser(intent, mUserTracker.getUserHandle());
                dismiss();
            }
        }
        if (v.getId() == com.android.systemui.res.R.id.iv_restart){
            if (ActivityManager.isUserAMonkey()) {
                return;
            }
            mWindowManagerFuncs.reboot(false);
        }
    }

    //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 start
    @Override
    public void dismiss() {
        try {
            mContext.unregisterReceiver(mPhoneStateReceiver);
            mNotifPipeline.removeCollectionListener(mAlarmNotificationListener);
        } catch (Exception e) {
            Log.w(TAG, "Receiver already unregistered");
        }
        super.dismiss();
    }
    //Redmine 334025 The phone call disappears on the shutdown interface by yangyang 2025.05.29 end

    private Bitmap takeScreenshot() {
        // Take the screenshot
        final ScreenCapture.SynchronousScreenCaptureListener syncScreenCapture =
                ScreenCapture.createSyncCaptureListener();
        try {
            WindowManagerGlobal.getWindowManagerService().captureDisplay(DEFAULT_DISPLAY, null,
                    syncScreenCapture);
        } catch (RemoteException e) {
            e.rethrowAsRuntimeException();
        }
        final ScreenCapture.ScreenshotHardwareBuffer screenshotBuffer = syncScreenCapture.getBuffer();
        final Bitmap screenShot = screenshotBuffer == null ? null : screenshotBuffer.asBitmap();
        if (screenShot == null) {
            return null;
        }
        screenShot.setHasAlpha(false);
        return screenShot;
    }

    private boolean isTouchInsideView(ImageView view, MotionEvent event) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);

        int viewLeft = location[0];
        int viewTop = location[1];
        int viewRight = viewLeft + view.getWidth();
        int viewBottom = viewTop + view.getHeight();

        float touchX = event.getRawX();
        float touchY = event.getRawY();

        return touchX >= viewLeft && touchX <= viewRight && touchY >= viewTop && touchY <= viewBottom;
    }

    @Override
    public boolean onLongClick(View v) {
        if (v.getId() == com.android.systemui.res.R.id.iv_power_off){
            if (ActivityManager.isUserAMonkey()) {
                return false;
            }
            if (!mUserManager.hasUserRestriction(UserManager.DISALLOW_SAFE_BOOT)) {
                dismiss();
                mWindowManagerFuncs.reboot(true);
                return true;
            }
            return false;
        }
        if (v.getId() == com.android.systemui.res.R.id.iv_restart){
            if (ActivityManager.isUserAMonkey()) {
                return false;
            }
            if (!mUserManager.hasUserRestriction(UserManager.DISALLOW_SAFE_BOOT)) {
                dismiss();
                mWindowManagerFuncs.reboot(true);
                return true;
            }
            return false;
        }
        return false;
    }
}


