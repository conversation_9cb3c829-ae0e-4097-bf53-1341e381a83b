package com.android.systemui.charginganimation.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;
import com.android.systemui.charginganimation.util.TextDrawUtil;
import com.android.systemui.charginganimation.util.TypedValueUtil;
import android.provider.Settings;
import com.android.systemui.res.R;

public class HwChargingView extends View {

    private String TAG = "HwChargingView";
    private int BATTERY_LOW_LEVEL = 20;
    private int BATTERY_MEDIUM_LEVEL = 80;
    private String batteryStatus;
    private int backGroundColor = Color.parseColor("#00000000");
    private int progress = 0;
    private Rect mTextBounds = new Rect();
    private Paint mPaint = new Paint();
    //private TextPaint mTextPaint = new TextPaint();
    private String charging = "";

    private int[] mColorArr = {
            Color.parseColor("#cb1c0e"),
            Color.parseColor("#f58a13"),
            Color.parseColor("#1dcc8b"),
            Color.parseColor("#2b4ede")
    };

    private int color;

    public HwChargingView(Context context) {
        this(context, null);
    }

    public HwChargingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private TextPaint mTextPaint;
    private TextPaint mStatusPaint;

    private void init(final Context context) {
        mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setColor(Color.WHITE);
        mTextPaint.setTextSize(TypedValueUtil.sp2px(context, 60));
        mTextPaint.setTextAlign(Paint.Align.LEFT);

        mStatusPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        mStatusPaint.setColor(Color.WHITE);
        mStatusPaint.setTextSize(TypedValueUtil.sp2px(context, 17));
    }

    @Override
    public void onDrawForeground(Canvas canvas) {
        super.onDrawForeground(canvas);
        setBackgroundColor(backGroundColor);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawProgress(canvas);

    }

    private void drawProgress(Canvas canvas) {
        float viewWidth = getWidth();
        float viewHeight = getHeight();
        float centerX = viewWidth / 2f;
        String batteryValue = String.format("%.0f", new Object[]{Float.valueOf(batteryStatus)});
        float mainSize = mTextPaint.getTextSize();
        float percentSize = TypedValueUtil.sp2px(mContext, 30);
        float baseLineY = TextDrawUtil.getVerticalCenterBaseline(viewHeight);
        TextDrawUtil.drawCombinedText(mContext,canvas, mTextPaint,
                batteryValue, mainSize,
                "%", percentSize,
                centerX, baseLineY);
        String statusText = progress < 100 ? charging : mContext.getResources().getString(R.string.sr_charged);
        mStatusPaint.setTextSize(TypedValueUtil.sp2px(getContext(), 17));
        mStatusPaint.setColor(Color.GRAY);

        Rect statusTextBounds = new Rect();
        mStatusPaint.getTextBounds(statusText, 0, statusText.length(), statusTextBounds);

        float statusBaseLine = baseLineY + statusTextBounds.height() - statusTextBounds.bottom + 30;
        TextDrawUtil.drawCenteredText(canvas, mStatusPaint, statusText, centerX, statusBaseLine);

        float spacing = TypedValueUtil.dp2px(mContext, 8);
        if(Settings.Global.getInt(mContext.getContentResolver(), "isFastCharging",0)==1){
            TextDrawUtil.drawScaledIcon(canvas, getContext(), R.drawable.ic_sr_fast_charge,
                    30, // 30dp
                    centerX, statusBaseLine,
                    spacing + (float) statusTextBounds.height() / 2);
        }else {
            TextDrawUtil.drawScaledIcon(canvas, getContext(), R.drawable.ic_sr_charge,
                    30, // 30dp
                    centerX, statusBaseLine,
                    spacing + (float) statusTextBounds.height() / 2);
        }
    }

    public void setProgress(int percent) {
        this.progress = percent;
        batteryStatus = mContext.getResources().getString(R.string.sr_cur_battery_percentage, progress);
        mTextPaint.getTextBounds(batteryStatus, 0, batteryStatus.length(), mTextBounds);
        if(Settings.Global.getInt(mContext.getContentResolver(), "isFastCharging",0)==1){
            charging = mContext.getResources().getString(R.string.sr_fast_charging);
        }else{
            charging = mContext.getResources().getString(R.string.sr_charging);
        }
    }

    private void changeColor(int color) {
        if (color == this.color) return;
        this.color = color;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }
}
