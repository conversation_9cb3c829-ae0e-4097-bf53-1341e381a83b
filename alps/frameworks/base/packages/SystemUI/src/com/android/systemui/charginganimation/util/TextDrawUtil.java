// TextDrawUtil.java
package com.android.systemui.charginganimation.util;

import android.content.Context;
import android.graphics.*;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;

import java.text.DecimalFormatSymbols;
import java.util.Locale;

public class TextDrawUtil {
    public static void drawCombinedText(Context context, Canvas canvas, TextPaint paint,
            String mainText, float mainSize,
            String suffixText, float suffixSize,
            float centerX, float baselineY) {
        final float originSize = paint.getTextSize();
        Locale locale = context.getResources().getConfiguration().getLocales().get(0);
        char percentChar = DecimalFormatSymbols.getInstance(locale).getPercent();
        suffixText = String.valueOf(percentChar);
        paint.setTextSize(mainSize);
        float mainWidth = paint.measureText(mainText);
        paint.setTextSize(suffixSize);
        float suffixWidth = paint.measureText(suffixText);
        float totalWidth = mainWidth + suffixWidth;
        float startX = centerX - totalWidth / 2;
        boolean isRTLLanguage = TextUtils.getLayoutDirectionFromLocale(locale) == View.LAYOUT_DIRECTION_RTL;
        paint.setTextSize(isRTLLanguage ? suffixSize : mainSize);
        Rect statusTextBounds = new Rect();
        paint.getTextBounds(mainText, 0, mainText.length(), statusTextBounds);
        canvas.drawText(
                isRTLLanguage ? suffixText : mainText,
                startX,
                baselineY - statusTextBounds.bottom,
                paint
        );
        paint.setTextSize(isRTLLanguage ? mainSize : suffixSize);
        canvas.drawText(
                isRTLLanguage ? mainText : suffixText,
                startX + (isRTLLanguage ? suffixWidth : mainWidth),
                baselineY - statusTextBounds.bottom,
                paint
        );
        paint.setTextSize(originSize);
    }

    public static void drawCenteredText(Canvas canvas, TextPaint paint,
            String text, float centerX, float baselineY) {
        float textWidth = paint.measureText(text);
        canvas.drawText(text, centerX - textWidth / 2, baselineY , paint);
    }

    public static void drawScaledIcon(Canvas canvas, Context context,
            int resId, float sizeDp,
            float centerX, float baseLineY,
            float verticalSpacing) {
        Bitmap bitmap = BitmapFactory.decodeResource(context.getResources(), resId);
        if (bitmap == null) return;

        int sizePx = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, sizeDp, context.getResources().getDisplayMetrics());
        Bitmap scaled = Bitmap.createScaledBitmap(bitmap, sizePx, sizePx, true);

        float posX = centerX - scaled.getWidth() / 2f;
        float posY = baseLineY + verticalSpacing;

        canvas.drawBitmap(scaled, posX, posY, null);
        scaled.recycle();
        bitmap.recycle();
    }

    public static float getVerticalCenterBaseline(float height) {
        return height / 2;
    }
}