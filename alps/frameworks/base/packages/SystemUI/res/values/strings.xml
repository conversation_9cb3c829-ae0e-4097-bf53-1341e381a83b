<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2009, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Name of the status bar as seen in the applications info settings page. [CHAR LIMIT=12] -->
    <string name="app_label">System UI</string>

    <!-- When the battery is low, this is displayed to the user in a dialog.  The title of the low battery alert.  [CHAR LIMIT=NONE]-->
    <string name="battery_low_title">Turn on Battery Saver?</string>

    <!-- When the battery is low, this is displayed to the user in a dialog.  The description of the low battery alert.  [CHAR LIMIT=NONE]-->
    <string name="battery_low_description">You have <xliff:g id="percentage" example="20%">%s</xliff:g> battery left. Battery Saver turns on Dark theme, restricts background activity, and delays notifications.</string>

    <!-- When the battery is low at first time, this is displayed to the user in a dialog.  The description of the low battery alert.  [CHAR LIMIT=NONE]-->
    <string name="battery_low_intro">Battery Saver turns on Dark theme, restricts background activity, and delays notifications.</string>

    <!-- A message that appears when the battery level is getting low in a dialog.  This is
        appended to the subtitle of the low battery alert.  "percentage" is the percentage of battery
        remaining [CHAR LIMIT=none]-->
    <string name="battery_low_percent_format"><xliff:g id="percentage">%s</xliff:g> remaining</string>

    <!-- SVG path description for the battery frame of the unified battery drawable
    (BatteryLayersDrawable.kt). Drawn on a 24x14 canvas. Not suitable outside in any other context -->
    <string name="battery_unified_frame_path_string" translatable="false">M2.75,3C2.75,1.757 3.757,0.75 5,0.75H20C21.795,0.75 23.25,2.205 23.25,4V10C23.25,11.795 21.795,13.25 20,13.25H5C3.757,13.25 2.75,12.243 2.75,11V3Z </string>

    <!-- A message that appears when the battery remaining estimate is low in a dialog.  This is
    appended to the subtitle of the low battery alert.  "percentage" is the percentage of battery
    remaining. "time" is the amount of time remaining before the phone runs out of battery [CHAR LIMIT=none]-->

    <!-- A message that appears when the battery remaining estimate is low in a dialog and insufficient
    data was present to say it is customized to the user. This is appended to the subtitle of the
    low battery alert.  "percentage" is the percentage of battery remaining. "time" is the amount
     of time remaining before the phone runs out of battery [CHAR LIMIT=none]-->

    <!-- Same as battery_low_percent_format, with a notice about battery saver if on. [CHAR LIMIT=none]-->

    <!-- A message that appears when a USB charger is plugged in and the device does not
    support charging on it.  That is, a charger that fits into the USB port and goes into
    a wall socket, not into a computer. (This happens because some devices require more
    current than the USB spec allows.  [CHAR LIMIT=NONE] -->

    <!-- First line of invalid_charger, used in the notification form.  [CHAR LIMIT=NONE]-->
    <string name="invalid_charger_title">Can\'t charge via USB</string>

    <!-- Second line of invalid_charger, used in the notification form.  [CHAR LIMIT=NONE]-->
    <string name="invalid_charger_text">Use the charger that came with your device</string>

    <!-- Battery saver confirmation dialog title [CHAR LIMIT=NONE]-->
    <string name="battery_saver_confirmation_title">Turn on Battery Saver?</string>

    <!-- The more generic version of the battery saver confirmation dialog title [CHAR LIMIT=NONE] -->
    <string name="battery_saver_confirmation_title_generic">About Battery Saver</string>

    <!-- Battery saver confirmation dialog ok text [CHAR LIMIT=40]-->
    <string name="battery_saver_confirmation_ok">Turn on</string>

    <!-- Battery saver notification action [CHAR LIMIT=NONE]-->
    <string name="battery_saver_start_action">Turn on</string>

    <!-- Battery saver notification dismiss action: Do not turn on battery saver. [CHAR LIMIT=NONE]-->
    <string name="battery_saver_dismiss_action">No thanks</string>

    <!-- Secondary label for Battery Saver tile when Battery Saver is enabled. [CHAR LIMIT=20] -->
    <string name="standard_battery_saver_text">Standard</string>
    <!-- Secondary label for Battery Saver tile when Extreme Battery Saver is enabled. [CHAR LIMIT=20] -->
    <string name="extreme_battery_saver_text">Extreme</string>

    <!-- Name of the button that links to the Settings app. [CHAR LIMIT=NONE] -->

    <!-- Name of the button that links to the Wifi settings screen. [CHAR LIMIT=NONE] -->

    <!-- Label in system panel saying the device will use the orientation sensor to rotate [CHAR LIMIT=30] -->
    <string name="status_bar_settings_auto_rotation">Auto-rotate screen</string>

    <!-- Separator for PLMN and SPN in network name. -->
    <string name="status_bar_network_name_separator" translatable="false"> - </string>

    <!-- Prompt for the USB device permission dialog [CHAR LIMIT=80] -->
    <string name="usb_device_permission_prompt">Allow <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to access <xliff:g id="usb_device" example="USB Headphones">%2$s</xliff:g>?</string>

    <!-- Checkbox label for USB device dialogs with warning text for USB device dialogs.  [CHAR LIMIT=200]-->
    <string name="usb_device_permission_prompt_warn">Allow <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to access <xliff:g id="usb_device" example="USB Headphones">%2$s</xliff:g>?\nThis app has not been granted record permission but could capture audio through this USB device.</string>

    <!-- USB audio device permission dialog title.  [CHAR LIMIT=200]-->
    <string name="usb_audio_device_permission_prompt_title">Allow <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to access <xliff:g id="usb_device" example="USB Headphones">%2$s</xliff:g>?</string>

    <!-- USB audio device confirm dialog title.  [CHAR LIMIT=200]-->
    <string name="usb_audio_device_confirm_prompt_title">Open <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to handle <xliff:g id="usb_device" example="USB Headphones">%2$s</xliff:g>?</string>

    <!-- Checkbox label for USB audio device dialogs with warning text for USB audio device dialogs.  [CHAR LIMIT=NONE]-->
    <string name="usb_audio_device_prompt_warn">This app has not been granted record permission but could capture audio through this USB device. Using <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> with this device might prevent hearing calls, notifications and alarms.</string>

    <!-- Prompt for the USB audio device permission dialog [CHAR LIMIT=NONE] -->
    <string name="usb_audio_device_prompt">Using <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> with this device might prevent hearing calls, notifications and alarms.</string>

    <!-- Prompt for the USB accessory permission dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_permission_prompt">Allow <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to access <xliff:g id="usb_accessory"  example="USB Dock">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB device confirm dialog [CHAR LIMIT=80] -->
    <string name="usb_device_confirm_prompt">Open <xliff:g id="application">%1$s</xliff:g> to handle <xliff:g id="usb_device">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB device confirm dialog with warning text for USB device dialogs.  [CHAR LIMIT=200] -->
    <string name="usb_device_confirm_prompt_warn">Open <xliff:g id="application" example= "Usb Mega Player">%1$s</xliff:g> to handle <xliff:g id="usb_device" example="USB Headphones">%2$s</xliff:g>?\nThis app has not been granted record permission but could capture audio through this USB device.</string>

    <!-- Prompt for the USB accessory confirm dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_confirm_prompt">Open <xliff:g id="application">%1$s</xliff:g> to handle <xliff:g id="usb_accessory">%2$s</xliff:g>?</string>

    <!-- Prompt for the USB accessory URI dialog [CHAR LIMIT=80] -->
    <string name="usb_accessory_uri_prompt">No installed apps work with this USB accessory. Learn more about this accessory at <xliff:g id="url">%1$s</xliff:g></string>

    <!-- Title for USB accessory dialog.  Used when the name of the accessory cannot be determined.  [CHAR LIMIT=50] -->
    <string name="title_usb_accessory">USB accessory</string>

    <!-- View button label for USB dialogs.  [CHAR LIMIT=15] -->
    <string name="label_view">View</string>

    <!-- Checkbox label for USB device dialogs.  [CHAR LIMIT=50] -->
    <string name="always_use_device">Always open <xliff:g id="application">%1$s</xliff:g> when <xliff:g id="usb_device">%2$s</xliff:g> is connected</string>

    <!-- Checkbox label for USB accessory dialogs.  [CHAR LIMIT=50]-->
    <string name="always_use_accessory">Always open <xliff:g id="application">%1$s</xliff:g> when <xliff:g id="usb_accessory">%2$s</xliff:g> is connected</string>

    <!-- Title of confirmation dialog for USB debugging -->
    <string name="usb_debugging_title">Allow USB debugging?</string>

    <!-- Message of confirmation dialog for USB debugging -->
    <string name="usb_debugging_message">The computer\'s RSA key fingerprint is:\n<xliff:g id="fingerprint">%1$s</xliff:g></string>

    <!-- Option to always allow USB debugging from the attached computer -->
    <string name="usb_debugging_always">Always allow from this computer</string>

    <!-- Button label for confirming acceptance of enabling USB debugging [CHAR LIMIT=15] -->
    <string name="usb_debugging_allow">Allow</string>

    <!-- Title of notification shown when trying to enable USB debugging but a secondary user is the current foreground user. -->
    <string name="usb_debugging_secondary_user_title">USB debugging not allowed</string>

    <!-- Message of notification shown when trying to enable USB debugging but a secondary user is the current foreground user. -->
    <string name="usb_debugging_secondary_user_message">The user currently signed in to this device can\'t turn on USB debugging. To use this feature, switch to an admin user.</string>

    <!-- Title of confirmation dialog for wireless debugging [CHAR LIMIT=80] -->
    <string name="hdmi_cec_set_menu_language_title">Do you want to change the system language to <xliff:g id="language" example="German">%1$s</xliff:g>?</string>

    <!-- Description for the <Set Menu Language> confirmation dialog [CHAR LIMIT=NONE] -->
    <string name="hdmi_cec_set_menu_language_description">System language change requested by another device</string>

    <!-- Button label for accepting language change [CHAR LIMIT=25] -->
    <string name="hdmi_cec_set_menu_language_accept">Change language</string>

    <!-- Button label for declining language change [CHAR LIMIT=25] -->
    <string name="hdmi_cec_set_menu_language_decline">Keep current language</string>

    <!-- Button label to share wifi [CHAR_LIMIT=20] -->
    <string name="share_wifi_button_text">Share Wi\u2011Fi</string>

    <!-- Title of confirmation dialog for wireless debugging [CHAR LIMIT=NONE] -->
    <string name="wifi_debugging_title">Allow wireless debugging on this network?</string>

    <!-- Message of confirmation dialog for wireless debugging [CHAR LIMIT=NONE] -->
    <string name="wifi_debugging_message">Network Name (SSID)\n<xliff:g id="ssid" example="My wifi">%1$s</xliff:g>\n\nWi\u2011Fi Address (BSSID)\n<xliff:g id="bssid" example="AB:CD:EF:12:34:56">%2$s</xliff:g></string>

    <!-- Option to always allow wireless debugging on this network [CHAR LIMIT=NONE] -->
    <string name="wifi_debugging_always">Always allow on this network</string>

    <!-- Button label for confirming acceptance of enabling wireless debugging [CHAR LIMIT=15] -->
    <string name="wifi_debugging_allow">Allow</string>

    <!-- Title of notification shown when trying to enable wireless debugging but a secondary user is the current foreground user. [CHAR LIMIT=NONE] -->
    <string name="wifi_debugging_secondary_user_title">Wireless debugging not allowed</string>

    <!-- Message of notification shown when trying to enable wireless debugging but a secondary user is the current foreground user. [CHAR LIMIT=NONE] -->
    <string name="wifi_debugging_secondary_user_message">The user currently signed in to this device can\u2019t turn on wireless debugging. To use this feature, switch to an admin user.</string>

    <!-- Title of USB contaminant presence dialog [CHAR LIMIT=NONE] -->
    <string name="usb_contaminant_title">USB port disabled</string>

    <!-- Message of USB contaminant presence dialog [CHAR LIMIT=NONE] -->
    <string name="usb_contaminant_message">To protect your device from liquid or debris, the USB port is disabled and won\u2019t detect any accessories.\n\nYou\u2019ll be notified when it\u2019s okay to use the USB port again.</string>

    <!-- Toast for enabling ports from USB contaminant dialog [CHAR LIMIT=NONE] -->
    <string name="usb_port_enabled">USB port enabled to detect chargers and accessories</string>

    <!-- Button text to disable contaminant detection [CHAR LIMIT=NONE] -->
    <string name="usb_disable_contaminant_detection">Enable USB</string>

    <!-- Button text to disable contaminant detection [CHAR LIMIT=NONE] -->
    <string name="learn_more">Learn more</string>

    <!-- Power menu item for taking a screenshot [CHAR LIMIT=20]-->
    <string name="global_action_screenshot">Screenshot</string>
    <!-- Message shown in power menu when Extend Unlock has been disabled [CHAR_LIMIT=NONE] -->
    <string name="global_action_smart_lock_disabled">Extend Unlock disabled</string>

    <!-- text to show in place of RemoteInput images when they cannot be shown.
         [CHAR LIMIT=50] -->
    <string name="remote_input_image_insertion_text">sent an image</string>

    <!-- Informs the user that a screenshot is being saved. [CHAR LIMIT=50] -->
    <string name="screenshot_saving_title">Saving screenshot\u2026</string>
    <!-- Informs the user that a screenshot is being saved. [CHAR LIMIT=50] -->
    <string name="screenshot_saving_work_profile_title">Saving screenshot to work profile\u2026</string>
    <!-- Informs the user that a screenshot is being saved to the private profile. [CHAR LIMIT=100] -->
    <string name="screenshot_saving_private_profile">Saving screenshot to private</string>
    <!-- Notification title displayed when a screenshot is saved to the Gallery. [CHAR LIMIT=50] -->
    <string name="screenshot_saved_title">Screenshot saved</string>
    <!-- Notification title displayed when we fail to take a screenshot. [CHAR LIMIT=50] -->
    <string name="screenshot_failed_title">Couldn\'t save screenshot</string>
    <!-- Appended to the notification content when a screenshot failure happens on an external display. [CHAR LIMIT=50] -->
    <string name="screenshot_failed_external_display_indication">External Display</string>
    <!-- Notification text displayed when we fail to save a screenshot due to locked storage. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_save_user_locked_text">Device must be unlocked before screenshot can be saved</string>
    <!-- Notification text displayed when we fail to save a screenshot for unknown reasons. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_save_unknown_text">Try taking screenshot again</string>
    <!-- Notification text displayed when we fail to save a screenshot. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_save_text">Can\'t save screenshot</string>
    <!-- Notification text displayed when we fail to take a screenshot. [CHAR LIMIT=100] -->
    <string name="screenshot_failed_to_capture_text">Taking screenshots isn\'t allowed by the app or
        your organization</string>
    <!-- Notification text displayed when screenshots are blocked by an IT admin. [CHAR LIMIT=100] -->
    <string name="screenshot_blocked_by_admin">Taking screenshots is blocked by your IT admin</string>
    <!-- Label for UI element which allows editing the screenshot [CHAR LIMIT=30] -->
    <string name="screenshot_edit_label">Edit</string>
    <!-- Content description indicating that tapping the element will allow editing the screenshot [CHAR LIMIT=NONE] -->
    <string name="screenshot_edit_description">Edit screenshot</string>
    <!-- Label for UI element which allows sharing the screenshot [CHAR LIMIT=30] -->
    <string name="screenshot_share_label">Share</string>
    <!-- Content description indicating that tapping the element will allow sharing the screenshot [CHAR LIMIT=NONE] -->
    <string name="screenshot_share_description">Share screenshot</string>
    <!-- Label for UI element which allows the user to capture additional off-screen content in a screenshot. [CHAR LIMIT=30] -->
    <string name="screenshot_scroll_label">Capture more</string>
    <!-- Content description indicating that tapping a button will dismiss the screenshots UI [CHAR LIMIT=NONE] -->
    <string name="screenshot_dismiss_description">Dismiss screenshot</string>
    <!-- Content description indicating that tapping a button will dismiss the work profile first run dialog [CHAR LIMIT=NONE] -->
    <string name="screenshot_dismiss_work_profile">Dismiss work profile message</string>
    <!-- Content description indicating that the view is a preview of the screenshot that was just taken [CHAR LIMIT=NONE] -->
    <string name="screenshot_preview_description">Screenshot preview</string>
    <!-- Content description for the top boundary of the screenshot being cropped, with the current position as a percentage. [CHAR LIMIT=NONE] -->
    <string name="screenshot_top_boundary_pct">Top boundary <xliff:g id="percent" example="50">%1$d</xliff:g> percent</string>
    <!-- Content description for the bottom boundary of the screenshot being cropped, with the current position as a percentage. [CHAR LIMIT=NONE] -->
    <string name="screenshot_bottom_boundary_pct">Bottom boundary <xliff:g id="percent" example="50">%1$d</xliff:g> percent</string>
    <!-- Content description for the left boundary of the screenshot being cropped, with the current position as a percentage. [CHAR LIMIT=NONE] -->
    <string name="screenshot_left_boundary_pct">Left boundary <xliff:g id="percent" example="50">%1$d</xliff:g> percent</string>
    <!-- Content description for the right boundary of the screenshot being cropped, with the current position as a percentage. [CHAR LIMIT=NONE] -->
    <string name="screenshot_right_boundary_pct">Right boundary <xliff:g id="percent" example="50">%1$d</xliff:g> percent</string>
    <!-- Notification displayed when a screenshot is saved in a work profile. [CHAR LIMIT=NONE] -->
    <string name="screenshot_work_profile_notification">Saved in <xliff:g id="app" example="Files">%1$s</xliff:g> in the work profile</string>
    <!-- Notification displayed when a screenshot is saved in the private profile. [CHAR LIMIT=NONE] -->
    <string name="screenshot_private_profile_notification">Saved in <xliff:g id="app" example="Files">%1$s</xliff:g> in the private profile</string>
    <!-- Default name referring to the app on the device that lets the user browse stored files. [CHAR LIMIT=NONE] -->
    <string name="screenshot_default_files_app_name">Files</string>
    <!-- A notice shown to the user to indicate that an app has detected the screenshot that the user has just taken. [CHAR LIMIT=75] -->
    <string name="screenshot_detected_template"><xliff:g id="appName" example="Google Chrome">%1$s</xliff:g> detected this screenshot.</string>
    <!-- A notice shown to the user to indicate that multiple apps have detected the screenshot that the user has just taken. [CHAR LIMIT=75] -->
    <string name="screenshot_detected_multiple_template"><xliff:g id="appName" example="Google Chrome">%1$s</xliff:g> and other open apps detected this screenshot.</string>
    <!-- Add to note button used in App Clips flow to return the saved screenshot image to notes app. [CHAR LIMIT=NONE] -->
    <string name="app_clips_save_add_to_note">Add to note</string>

    <!-- Notification title displayed for screen recording [CHAR LIMIT=50]-->
    <string name="screenrecord_title">Screen Recorder</string>
    <!-- Processing screen recoding video in the background [CHAR LIMIT=30]-->
    <string name="screenrecord_background_processing_label">Processing screen recording</string>
    <!-- Description of the screen recording notification channel [CHAR LIMIT=NONE]-->
    <string name="screenrecord_channel_description">Ongoing notification for a screen record session</string>

    <!-- For updated Screen Recording permission dialog (i.e. with PSS)-->
    <!-- Title for the screen prompting the user to begin recording their screen [CHAR LIMIT=NONE]-->
    <string name="screenrecord_permission_dialog_title">Start Recording?</string>
    <!-- Message reminding the user that sensitive information may be captured during a full screen recording for the updated dialog that includes partial screen sharing option [CHAR_LIMIT=350]-->
    <string name="screenrecord_permission_dialog_warning_entire_screen">While you’re recording, Android has access to anything visible on your screen or played on your device. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- Message reminding the user that sensitive information may be captured during a single app screen recording for the updated dialog that includes partial screen sharing option [CHAR_LIMIT=350]-->
    <string name="screenrecord_permission_dialog_warning_single_app">While you’re recording an app, Android has access to anything shown or played on that app. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- Button to start a screen recording in the updated screen record dialog that allows to select an app to record [CHAR LIMIT=50]-->
    <string name="screenrecord_permission_dialog_continue">Start recording</string>

    <!-- Label for a switch to enable recording audio [CHAR LIMIT=NONE]-->
    <string name="screenrecord_audio_label">Record audio</string>
    <!-- Label for the option to record audio from the device [CHAR LIMIT=NONE]-->
    <string name="screenrecord_device_audio_label">Device audio</string>
    <!-- Description of what audio may be captured from the device [CHAR LIMIT=NONE]-->
    <string name="screenrecord_device_audio_description">Sound from your device, like music, calls, and ringtones</string>
    <!-- Label for the option to enable microphone input during screen recording [CHAR LIMIT=NONE]-->
    <string name="screenrecord_mic_label">Microphone</string>
    <!-- Label for an option to record audio from both device and microphone [CHAR LIMIT=NONE]-->
    <string name="screenrecord_device_audio_and_mic_label">Device audio and microphone</string>
    <!-- Button to start a screen recording [CHAR LIMIT=50]-->
    <string name="screenrecord_continue">Start</string>
    <!-- Notification text displayed when we are recording the screen [CHAR LIMIT=100]-->
    <string name="screenrecord_ongoing_screen_only">Recording screen</string>
    <!-- Notification text displayed when we are recording both the screen and audio [CHAR LIMIT=100]-->
    <string name="screenrecord_ongoing_screen_and_audio">Recording screen and audio</string>
    <!-- Label for the checkbox to enable showing location of touches during screen recording [CHAR LIMIT=NONE]-->
    <string name="screenrecord_taps_label">Show touches on screen</string>
    <!-- Label for notification that the user can tap to stop and save the screen recording [CHAR LIMIT=NONE] -->
    <!-- Label for notification action to stop and save the screen recording [CHAR LIMIT=35] -->
    <string name="screenrecord_stop_label">Stop</string>
    <!-- Label for notification action to share screen recording [CHAR LIMIT=35] -->
    <string name="screenrecord_share_label">Share</string>
    <!-- A toast message shown after successfully canceling a screen recording [CHAR LIMIT=NONE] -->
    <!-- Notification text shown after saving a screen recording [CHAR LIMIT=100] -->
    <string name="screenrecord_save_title">Screen recording saved</string>
    <!-- Subtext for a notification shown after saving a screen recording to prompt the user to view it [CHAR_LIMIT=100] -->
    <string name="screenrecord_save_text">Tap to view</string>
    <!-- A toast message shown when there is an error saving a screen recording [CHAR LIMIT=NONE] -->
    <string name="screenrecord_save_error">Error saving screen recording</string>
    <!-- A toast message shown when the screen recording cannot be started due to a generic error [CHAR LIMIT=NONE] -->
    <string name="screenrecord_start_error">Error starting screen recording</string>
    <!-- Title for a dialog shown to the user that will let them stop recording their screen [CHAR LIMIT=50] -->
    <string name="screenrecord_stop_dialog_title">Stop recording screen?</string>
    <!-- Text telling a user that they will stop recording their screen if they click the "Stop recording" button [CHAR LIMIT=100] -->
    <string name="screenrecord_stop_dialog_message">You will stop recording your screen</string>
    <!-- Text telling a user that they will stop recording the contents of the specified [app_name] if they click the "Stop recording" button. Note that the app name will appear in bold. [CHAR LIMIT=100] -->
    <string name="screenrecord_stop_dialog_message_specific_app">You will stop recording &lt;b><xliff:g id="app_name" example="Photos App">%1$s</xliff:g>&lt;/b></string>
    <!-- Button to stop a screen recording [CHAR LIMIT=35] -->
    <string name="screenrecord_stop_dialog_button">Stop recording</string>

    <!-- Title for a dialog shown to the user that will let them stop sharing their screen to another app on the device [CHAR LIMIT=50] -->
    <string name="share_to_app_stop_dialog_title">Stop sharing screen?</string>
    <!-- Text telling a user that they will stop sharing their screen if they click the "Stop sharing" button [CHAR LIMIT=100] -->
    <string name="share_to_app_stop_dialog_message">You will stop sharing your screen</string>
    <!-- Text telling a user that they will stop sharing the contents of the specified [app_name] if they click the "Stop sharing" button. Note that the app name will appear in bold. [CHAR LIMIT=100] -->
    <string name="share_to_app_stop_dialog_message_specific_app">You will stop sharing &lt;b><xliff:g id="app_name" example="Photos App">%1$s</xliff:g>&lt;/b></string>
    <!-- Button to stop screen sharing [CHAR LIMIT=35] -->
    <string name="share_to_app_stop_dialog_button">Stop sharing</string>

    <!-- Title for a dialog shown to the user that will let them stop casting their screen to a different device [CHAR LIMIT=50] -->
    <string name="cast_to_other_device_stop_dialog_title">Stop casting screen?</string>
    <!-- Text telling a user that they will stop casting their screen to a different device if they click the "Stop casting" button [CHAR LIMIT=100] -->
    <string name="cast_to_other_device_stop_dialog_message">You will stop casting your screen</string>
    <!-- Text telling a user that they will stop casting the contents of the specified [app_name] to a different device if they click the "Stop casting" button. Note that the app name will appear in bold.  [CHAR LIMIT=100] -->
    <string name="cast_to_other_device_stop_dialog_message_specific_app">You will stop casting &lt;b><xliff:g id="app_name" example="Photos App">%1$s</xliff:g>&lt;/b></string>
    <!-- Button to stop screen casting to a different device [CHAR LIMIT=35] -->
    <string name="cast_to_other_device_stop_dialog_button">Stop casting</string>

    <!-- Button to close a dialog without doing any action [CHAR LIMIT=20] -->
    <string name="close_dialog_button">Close</string>

    <!-- Notification title displayed for issue recording [CHAR LIMIT=50]-->
    <string name="issuerecord_title">Issue Recorder</string>
    <!-- Processing issue recoding data in the background [CHAR LIMIT=30]-->
    <string name="issuerecord_background_processing_label">Processing issue recording</string>
    <!-- Description of the screen recording notification channel [CHAR LIMIT=NONE]-->
    <string name="issuerecord_channel_description">Ongoing notification for an issue collection session</string>

    <!-- Notification text displayed when we are recording the screen [CHAR LIMIT=100]-->
    <string name="issuerecord_ongoing_screen_only">Recording issue</string>
    <!-- Label for notification action to share issue recording [CHAR LIMIT=35] -->
    <string name="issuerecord_share_label">Share</string>
    <!-- A toast message shown after successfully canceling a issue recording [CHAR LIMIT=NONE] -->
    <!-- Notification text shown after saving a issue recording [CHAR LIMIT=100] -->
    <string name="issuerecord_save_title">Issue recording saved</string>
    <!-- Subtext for a notification shown after saving a issue recording to prompt the user to view it [CHAR_LIMIT=100] -->
    <string name="issuerecord_save_text">Tap to view</string>
    <!-- A toast message shown when there is an error saving a issue recording [CHAR LIMIT=NONE] -->
    <string name="issuerecord_save_error">Error saving issue recording</string>
    <!-- A toast message shown when the issue recording cannot be started due to a generic error [CHAR LIMIT=NONE] -->
    <string name="issuerecord_start_error">Error starting issue recording</string>

    <!-- Cling help message title when hiding the navigation bar entering immersive mode [CHAR LIMIT=none] -->
    <string name="immersive_cling_title">Viewing full screen</string>
    <!-- Cling help message description when hiding the navigation bar entering immersive mode [CHAR LIMIT=none] -->
    <string name="immersive_cling_description">To exit, swipe down from the top of your screen</string>
    <!-- Cling help message confirmation button when hiding the navigation bar entering immersive mode [CHAR LIMIT=30] -->
    <string name="immersive_cling_positive">Got it</string>

    <!-- Content description of the back button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_back">Back</string>
    <!-- Content description of the home button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_home">Home</string>
    <!-- Content description of the menu button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_menu">Menu</string>
    <!-- Content description of the accessibility button in the navigation bar (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_accessibility_button">Accessibility</string>
    <!-- Content description of the rotate button in the navigation bar (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotate_button">Rotate screen</string>
    <!-- Content description of the recents button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_recent">Overview</string>
    <!-- Content description of the search button for accessibility. [CHAR LIMIT=NONE] -->
    <!-- Content description of the camera button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_camera_button">Camera</string>
    <!-- Content description of the phone button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_phone_button">Phone</string>
    <!-- Content description of the phone button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_voice_assist_button">Voice Assist</string>
    <!-- Content description of the wallet button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_wallet_button">Wallet</string>
    <!-- Content description of the QR Code scanner for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_qr_code_scanner_button">QR Code Scanner</string>
    <!-- Content description of the unlock button for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_unlock_button">Unlocked</string>
    <!-- Content description of the lock icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_lock_icon">Device locked</string>
    <!-- Content description hint of the unlock button when fingerprint is on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <!-- Accessibility action of the unlock button when fingerpint is on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <!-- Content description of the Trusted Face icon for accessibility. [CHAR LIMIT=NONE] -->
    <string name="accessibility_scanning_face">Scanning face</string>
    <!-- Click action label for accessibility for the smart reply buttons (not shown on-screen).". [CHAR LIMIT=NONE] -->
    <string name="accessibility_send_smart_reply">Send</string>
    <!-- Button name for "Cancel". [CHAR LIMIT=NONE] -->
    <string name="cancel">Cancel</string>

    <!-- Content description for the app logo icon on biometric prompt. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_logo">App logo</string>
    <!-- List of packages for which we want to show overridden logo. For example, an app overrides its launcher logo, if it's in this array, biometric dialog shows the overridden logo; otherwise biometric dialog still shows the default application info icon. [CHAR LIMIT=NONE] -->
    <string-array name="biometric_dialog_package_names_for_logo_with_overrides" />
    <!-- Message shown when a biometric is authenticated, asking the user to confirm authentication [CHAR LIMIT=30] -->
    <string name="biometric_dialog_confirm">Confirm</string>
    <!-- Button name on BiometricPrompt shown when a biometric is detected but not authenticated. Tapping the button resumes authentication [CHAR LIMIT=30] -->
    <string name="biometric_dialog_try_again">Try again</string>
    <!-- Content description for empty spaces that are not taken by the biometric dialog. Clicking on these areas will cancel authentication and dismiss the biometric dialog [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_empty_space_description">Tap to cancel authentication</string>
    <!-- Content description for the face icon when the device is not authenticating anymore [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_face_icon_description_idle">Please try again</string>
    <!-- Content description for the face icon when the device is authenticating [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_face_icon_description_authenticating">Looking for your face</string>
    <!-- Content description for the face icon when the user has been authenticated [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_face_icon_description_authenticated">Face authenticated</string>
    <!-- Content description for the face icon when the user has been authenticated and the confirm button has been pressed [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_face_icon_description_confirmed">Confirmed</string>
    <!-- Message shown when a biometric is authenticated, waiting for the user to confirm authentication [CHAR LIMIT=40]-->
    <string name="biometric_dialog_tap_confirm">Tap Confirm to complete</string>
    <!-- Message shown when a biometric has authenticated with a user's face and is waiting for the user to confirm authentication [CHAR LIMIT=60]-->
    <string name="biometric_dialog_tap_confirm_with_face">Unlocked by face</string>
    <!-- Message shown when a biometric has authenticated with a user's face and is waiting for the user to confirm authentication [CHAR LIMIT=60]-->
    <string name="biometric_dialog_tap_confirm_with_face_alt_1">Unlocked by face. Press to continue.</string>
    <!-- Message shown when a biometric has authenticated with a user's face and is waiting for the user to confirm authentication [CHAR LIMIT=60]-->
    <string name="biometric_dialog_tap_confirm_with_face_alt_2">Face recognized. Press to continue.</string>
    <!-- Message shown when a biometric has authenticated with a user's face and is waiting for the user to confirm authentication [CHAR LIMIT=60]-->
    <string name="biometric_dialog_tap_confirm_with_face_alt_3">Face recognized. Press the unlock icon to continue.</string>
    <!-- Message shown when a biometric has authenticated with a user's face and is waiting for the user to confirm authentication with SFPS [CHAR LIMIT=60]-->
    <string name="biometric_dialog_tap_confirm_with_face_sfps">Unlocked by face. Tap to continue.</string>
    <!-- Talkback string when a biometric is authenticated [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_authenticated">Authenticated</string>
    <!-- Talkback string when a canceling authentication [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_cancel_authentication">Cancel Authentication</string>
    <!-- Content description for the more options button on biometric prompt content view. [CHAR LIMIT=60] -->
    <string name="biometric_dialog_content_view_more_options_button">More Options</string>

    <!-- Button text shown on BiometricPrompt giving the user the option to use an alternate form of authentication (Pin) [CHAR LIMIT=30] -->
    <string name="biometric_dialog_use_pin">Use PIN</string>
    <!-- Button text shown on BiometricPrompt giving the user the option to use an alternate form of authentication (Pattern) [CHAR LIMIT=30] -->
    <string name="biometric_dialog_use_pattern">Use pattern</string>
    <!-- Button text shown on BiometricPrompt giving the user the option to use an alternate form of authentication (Pass) [CHAR LIMIT=30] -->
    <string name="biometric_dialog_use_password">Use password</string>
    <!-- Error string shown when the user enters an incorrect PIN [CHAR LIMIT=40]-->
    <string name="biometric_dialog_wrong_pin">Wrong PIN</string>
    <!-- Error string shown when the user enters an incorrect pattern [CHAR LIMIT=40]-->
    <string name="biometric_dialog_wrong_pattern">Wrong pattern</string>
    <!-- Error string shown when the user enters an incorrect password [CHAR LIMIT=40]-->
    <string name="biometric_dialog_wrong_password">Wrong password</string>
    <!-- Error string shown when the user enters too many incorrect attempts [CHAR LIMIT=120]-->
    <string name="biometric_dialog_credential_too_many_attempts">Too many incorrect attempts.\nTry again in <xliff:g id="number">%d</xliff:g> seconds.</string>
    <!-- Button text shown on an authentication screen giving the user the option to make an emergency call without unlocking their device [CHAR LIMIT=20] -->
    <string name="work_challenge_emergency_button_text">Emergency</string>

    <!-- Error string shown when the user enters an incorrect PIN/pattern/password and it counts towards the max attempts before the data on the device is wiped. [CHAR LIMIT=NONE]-->
    <string name="biometric_dialog_credential_attempts_before_wipe">Try again. Attempt <xliff:g id="attempts" example="1">%1$d</xliff:g> of <xliff:g id="max_attempts" example="3">%2$d</xliff:g>.</string>
    <!-- Title of a dialog shown when the user only has one attempt left to provide the correct PIN/pattern/password before the device, one of its users, or a work profile is wiped. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_attempt_before_wipe_dialog_title">Your data will be deleted</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct lock pattern before the device is wiped. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_device">If you enter an incorrect pattern on the next attempt, this device\u2019s data will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct PIN before the device is wiped. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pin_attempt_before_wipe_device">If you enter an incorrect PIN on the next attempt, this device\u2019s data will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct password before the device is wiped. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_password_attempt_before_wipe_device">If you enter an incorrect password on the next attempt, this device\u2019s data will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct lock pattern before the user is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_user">If you enter an incorrect pattern on the next attempt, this user will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct PIN before the user is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pin_attempt_before_wipe_user">If you enter an incorrect PIN on the next attempt, this user will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct password before the user is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_password_attempt_before_wipe_user">If you enter an incorrect password on the next attempt, this user will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct pattern before the work profile is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pattern_attempt_before_wipe_profile">If you enter an incorrect pattern on the next attempt, your work profile and its data will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct PIN before the work profile is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_pin_attempt_before_wipe_profile">If you enter an incorrect PIN on the next attempt, your work profile and its data will be deleted.</string>
    <!-- Content of a dialog shown when the user only has one attempt left to provide the correct password before the work profile is removed. [CHAR LIMIT=NONE] -->
    <string name="biometric_dialog_last_password_attempt_before_wipe_profile">If you enter an incorrect password on the next attempt, your work profile and its data will be deleted.</string>
    <!-- Confirmation button label for a dialog shown when the system requires the user to re-enroll their biometrics. [CHAR LIMIT=20] -->
    <string name="biometric_re_enroll_dialog_confirm">Set up</string>
    <!-- Cancel button label for a dialog shown when the system requires the user to re-enroll their biometric. [CHAR LIMIT=20] -->
    <string name="biometric_re_enroll_dialog_cancel">Not now</string>
    <!-- Notification content shown when the system requires the user to re-enroll their biometrics. [CHAR LIMIT=NONE] -->
    <string name="biometric_re_enroll_notification_content">This is required to improve security and performance</string>
    <!-- Notification title shown when the system requires the user to re-enroll their fingerprint. [CHAR LIMIT=NONE] -->
    <string name="fingerprint_re_enroll_notification_title">Set up Fingerprint Unlock again</string>
    <!-- Name shown for system notifications related to the fingerprint unlock feature. [CHAR LIMIT=NONE] -->
    <string name="fingerprint_re_enroll_notification_name">Fingerprint Unlock</string>
    <!-- Title for a dialog shown when the system requires the user to re-enroll their fingerprint. [CHAR LIMIT=NONE] -->
    <string name="fingerprint_re_enroll_dialog_title">Set up Fingerprint Unlock</string>
    <!-- Content for a dialog shown when the system requires the user to re-enroll their fingerprint. [CHAR LIMIT=NONE] -->
    <string name="fingerprint_re_enroll_dialog_content">To set up Fingerprint Unlock again, your current fingerprint images and models will be deleted.\n\nAfter they\’re deleted, you\’ll need to set up Fingerprint Unlock again to use your fingerprint to unlock your phone or verify it\’s you.</string>
    <!-- Content for a dialog shown when the system requires the user to re-enroll their fingerprint (singular). [CHAR LIMIT=NONE] -->
    <string name="fingerprint_re_enroll_dialog_content_singular">To set up Fingerprint Unlock again, your current fingerprint images and model will be deleted.\n\nAfter they\’re deleted, you\’ll need to set up Fingerprint Unlock again to use your fingerprint to unlock your phone or verify it\’s you.</string>
    <!-- Content for a dialog shown when an error occurs while the user is trying to re-enroll their fingerprint. [CHAR LIMIT=NONE] -->
    <string name="fingerprint_reenroll_failure_dialog_content">Couldn\u2019t set up fingerprint unlock. Go to Settings to try again.</string>
    <!-- Notification title shown when the system requires the user to re-enroll their face. [CHAR LIMIT=NONE] -->
    <string name="face_re_enroll_notification_title">Set up Face Unlock again</string>
    <!-- Name shown for system notifications related to the face unlock feature. [CHAR LIMIT=NONE] -->
    <string name="face_re_enroll_notification_name">Face Unlock</string>
    <!-- Title for a dialog shown when the system requires the user to re-enroll their face. [CHAR LIMIT=NONE] -->
    <string name="face_re_enroll_dialog_title">Set up Face Unlock</string>
    <!-- Content for a dialog shown when the system requires the user to re-enroll their face. [CHAR LIMIT=NONE] -->
    <string name="face_re_enroll_dialog_content">To set up Face Unlock again, your current face model will be deleted.\n\nYou\’ll need to set up this feature again to use your face to unlock your phone.</string>
    <!-- Content for a dialog shown when an error occurs while the user is trying to re-enroll their face. [CHAR LIMIT=NONE] -->
    <string name="face_reenroll_failure_dialog_content">Couldn\u2019t set up face unlock. Go to Settings to try again.</string>
    <!-- Message shown when the system-provided fingerprint dialog is shown, asking for authentication -->
    <string name="fingerprint_dialog_touch_sensor">Touch the fingerprint sensor</string>
    <!-- Content description after successful auth when confirmation required -->
    <string name="fingerprint_dialog_authenticated_confirmation">Press the unlock icon to continue</string>
    <!-- Message shown to inform the user a face cannot be recognized and fingerprint should instead be used.[CHAR LIMIT=50] -->
    <string name="fingerprint_dialog_use_fingerprint_instead">Face not recognized. Use fingerprint instead.</string>
    <!-- Message shown to inform the user a face cannot be recognized and fingerprint should instead be used.[CHAR LIMIT=50] -->
    <string name="keyguard_face_failed_use_fp">@string/fingerprint_dialog_use_fingerprint_instead</string>
    <!-- Message shown to inform the user a face cannot be recognized. [CHAR LIMIT=25] -->
    <string name="keyguard_face_failed">Face not recognized</string>
    <!-- Message shown to suggest using fingerprint sensor to authenticate after another biometric failed. [CHAR LIMIT=25] -->
    <string name="keyguard_suggest_fingerprint">Use fingerprint instead</string>
    <!-- Message shown to inform the user that face unlock is not available. [CHAR LIMIT=59] -->
    <string name="keyguard_face_unlock_unavailable">Face Unlock unavailable</string>

    <!-- Content description of the bluetooth icon when connected for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_connected">Bluetooth connected.</string>

    <!-- Content description of the bluetooth device icon. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_device_icon">Bluetooth device icon</string>

    <!-- Content description of the bluetooth device settings gear icon. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_device_settings_gear">Click to configure device detail</string>
    <!-- Content description of the bluetooth device settings see all. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_device_settings_see_all">Click to see all devices</string>
    <!-- Content description of the bluetooth device settings pair new device. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_device_settings_pair_new_device">Click to pair new device</string>

    <!-- Content description of the battery when battery state is unknown for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_unknown">Battery percentage unknown.</string>

    <!-- Content description of the bluetooth label showing what we are connected to. [CHAR LIMIT=NONE] -->
    <string name="accessibility_bluetooth_name">Connected to <xliff:g id="bluetooth" example="Car Audio">%s</xliff:g>.</string>

    <!-- Content description of the cast label showing what we are connected to. [CHAR LIMIT=NONE] -->
    <string name="accessibility_cast_name">Connected to <xliff:g id="cast" example="TV">%s</xliff:g>.</string>

    <!-- Content description of an item with no signal and no connection for accessibility (not shown on the screen) [CHAR LIMIT=NONE] -->
    <string name="accessibility_not_connected">Not connected.</string>
    <!-- Content description of the roaming data connection type. [CHAR LIMIT=NONE] -->
    <string name="data_connection_roaming">Roaming</string>

    <!-- Content description of the cell data being disabled but shortened. [CHAR LIMIT=20] -->
    <string name="cell_data_off">Off</string>

    <!-- Content description of the airplane mode icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_airplane_mode">Airplane mode.</string>

    <!-- Content description of the VPN icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_vpn_on">VPN on.</string>

    <!-- Content description of the battery level icon for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level">Battery <xliff:g id="number">%d</xliff:g> percent.</string>

    <!-- Content description of the battery level icon for accessibility, including the estimated time remaining before the phone runs out of battery (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level_with_estimate">Battery <xliff:g id="percentage" example="95%">%1$d</xliff:g> percent, <xliff:g id="time" example="Until 3:15pm">%2$s</xliff:g></string>

    <!-- Content description of the battery level icon for accessibility while the device is charging (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level_charging">Battery charging, <xliff:g id="battery_percentage">%d</xliff:g> percent.</string>

    <!-- Content description of the battery level icon for accessibility, with information that the device charging is paused in order to protect the lifetime of the battery (not shown on screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level_charging_paused">Battery <xliff:g id="percentage" example="90%">%d</xliff:g> percent, charging paused for battery protection.</string>

    <!-- Content description of the battery level icon for accessibility, including the estimated time remaining before the phone runs out of battery *and* information that the device charging is paused in order to protect the lifetime of the battery (not shown on screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_battery_level_charging_paused_with_estimate">Battery <xliff:g id="percentage" example="90%">%1$d</xliff:g> percent, <xliff:g id="time" example="Until 3:15pm">%2$s</xliff:g>, charging paused for battery protection.</string>

    <!-- Content description of overflow icon container of the notifications for accessibility (not shown on the screen)[CHAR LIMIT=NONE] -->
    <string name="accessibility_overflow_action">See all notifications</string>

    <!-- Content description of the TeleTypewriter(TTY) enabled icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_tty_enabled">TeleTypewriter enabled.</string>

    <!-- Content description of the ringer vibrate icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ringer_vibrate">Ringer vibrate.</string>

    <!-- Content description of the ringer silent icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_ringer_silent">Ringer silent.</string>

    <!-- Content description of the cast icon in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_casting">@string/quick_settings_casting</string>

    <!-- Content description for the notification shade panel (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_notification_shade">Notification shade.</string>
    <!-- Content description for the quick settings panel (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_quick_settings">Quick settings.</string>
    <!-- Content description for the split notification shade that also includes QS (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_qs_notification_shade">Quick settings and Notification shade.</string>
    <!-- Content description for the lock screen (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_lock_screen">Lock screen</string>
    <!-- Content description for the work profile lock screen. This prevents work profile apps from being used, but personal apps can be used as normal (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_work_lock">Work lock screen</string>
    <!-- Content description for the close button in the zen mode panel introduction message. [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_close">Close</string>

    <!-- Content description of the do not disturb tile in quick settings when on in none (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_none_on">total silence</string>
    <!-- Content description of the do not disturb tile in quick settings when on in alarms only (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd_alarms_on">alarms only</string>
     <!-- Content description of the do not disturb tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_dnd">Do Not Disturb.</string>
    <!-- Content description of the bluetooth tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth">Bluetooth.</string>
    <!-- Content description of the bluetooth tile in quick settings when on (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_bluetooth_on">Bluetooth on.</string>
    <!-- Content description of the alarm tile in quick settings (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_alarm">Alarm set for <xliff:g id="time" example="Wed 3:30 PM">%s</xliff:g>.</string>
    <!-- Content description of zen mode time condition plus button (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_more_time">More time.</string>
    <!-- Content description of zen mode time condition minus button (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_less_time">Less time.</string>
    <!-- Announcement made when the screen stopped casting (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_casting_turned_off">Screen casting stopped.</string>

    <!-- Content description of the display brightness slider (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_brightness">Display brightness</string>

    <!-- Content description of the charging indicator on Ambient Display (lower-power version of the lock screen). [CHAR LIMIT=NONE] -->

    <!-- Title of dialog shown when mobile data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_mobile_title">Mobile data is paused</string>
    <!-- Title of dialog shown when data usage has exceeded limit and has been disabled. [CHAR LIMIT=48] -->
    <string name="data_usage_disabled_dialog_title">Data is paused</string>
    <!-- Body of dialog shown when data usage has exceeded limit and has been disabled. [CHAR LIMIT=NONE] -->
    <string name="data_usage_disabled_dialog">The data limit you set has been reached. You are no longer using mobile data.\n\nIf you resume, charges may apply for data usage.</string>
    <!-- Dialog button indicating that data connection should be re-enabled. [CHAR LIMIT=28] -->
    <string name="data_usage_disabled_dialog_enable">Resume</string>

    <!-- Accessibility text describing the presence of active location requests by one or more apps -->
    <string name="accessibility_location_active">Location requests active</string>

    <!-- Accessibility text describing sensors off active. [CHAR LIMIT=NONE] -->
    <string name="accessibility_sensors_off_active">Sensors off active</string>

    <!-- Content description of the clear button in the notification panel for accessibility (not shown on the screen). [CHAR LIMIT=NONE] -->
    <string name="accessibility_clear_all">Clear all notifications.</string>

    <!-- The overflow indicator shown when a group has more notification inside the group than the visible ones. An example is "+ 3" [CHAR LIMIT=5] -->
    <string name="notification_group_overflow_indicator">+ <xliff:g id="number" example="3">%s</xliff:g></string>

    <!-- Content description describing how many more notifications are in a group [CHAR LIMIT=NONE] -->
    <string name="notification_group_overflow_description">{count, plural,
        =1 {# more notification inside.}
        other {# more notifications inside.}
    }</string>

    <!-- Format to use to summarize a message from a contact in a single line of text. For example: "Julia: How's it going?". [CHAR LIMIT=NONE] -->

    <!-- Content description of button in notification inspector for system settings relating to
         notifications from this application [CHAR LIMIT=NONE] -->

    <!-- Content description of button in notification inspetor for application-provided settings
         for its own notifications [CHAR LIMIT=NONE] -->

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is on. [CHAR LIMIT=NONE] -->

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is off. [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_landscape">Screen is locked in landscape orientation.</string>

    <!-- Description of the button in the phone-style notification panel that controls auto-rotation, when auto-rotation is off. [CHAR LIMIT=NONE] -->
    <string name="accessibility_rotation_lock_on_portrait">Screen is locked in portrait orientation.</string>

    <!-- Announcement made when the screen is rotating automatically again (not shown on the screen). [CHAR LIMIT=NONE] -->

    <!-- Announcement made when the rotation lock state changes to landscape only (not shown on the screen). [CHAR LIMIT=NONE] -->

    <!-- Announcement made when the rotation lock state changes to portrait only (not shown on the screen).  [CHAR LIMIT=NONE] -->

    <!-- Name of the K-release easter egg: a display case for all our tastiest desserts. [CHAR LIMIT=30] -->
    <string name="dessert_case">Dessert Case</string>

    <!-- Name of the launcher shortcut icon that allows dreams to be started immediately [CHAR LIMIT=20] -->
    <string name="start_dreams">Screen saver</string>

    <!-- Textual description of Ethernet connections -->
    <string name="ethernet_label">Ethernet</string>

    <!-- QuickSettings: Onboarding text that introduces users to long press on an option in order to view the option's menu in Settings [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Do not disturb [CHAR LIMIT=NONE] -->
    <string name="quick_settings_dnd_label">Do Not Disturb</string>
    <!-- QuickSettings: Do not disturb - Priority only [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Do not disturb - Alarms only [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Do not disturb - Total silence [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Bluetooth [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_label">Bluetooth</string>
    <!-- QuickSettings: Bluetooth (Multiple) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Bluetooth (Off) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Bluetooth detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_detail_empty_text">No paired devices available</string>
    <!-- QuickSettings: Bluetooth dialog subtitle [CHAR LIMIT=NONE]-->
    <string name="quick_settings_bluetooth_tile_subtitle">Tap to connect or disconnect a device </string>
    <!-- QuickSettings: Bluetooth dialog pair new devices [CHAR LIMIT=NONE]-->
    <string name="pair_new_bluetooth_devices">Pair new device</string>
    <!-- QuickSettings: Bluetooth dialog see all devices [CHAR LIMIT=NONE]-->
    <string name="see_all_bluetooth_devices">See all</string>
    <!-- QuickSettings: Bluetooth dialog turn on Bluetooth [CHAR LIMIT=NONE]-->
    <string name="turn_on_bluetooth">Use Bluetooth</string>
    <!-- QuickSettings: Bluetooth dialog device connected default summary [CHAR LIMIT=NONE]-->
    <string name="quick_settings_bluetooth_device_connected">Connected</string>
    <!-- QuickSettings: Bluetooth dialog device in audio sharing default summary [CHAR LIMIT=50]-->
    <string name="quick_settings_bluetooth_device_audio_sharing">Audio Sharing</string>
    <!-- QuickSettings: Bluetooth dialog device saved default summary [CHAR LIMIT=NONE]-->
    <string name="quick_settings_bluetooth_device_saved">Saved</string>
    <!-- QuickSettings: Accessibility label to disconnect a device [CHAR LIMIT=NONE]-->
    <string name="accessibility_quick_settings_bluetooth_device_tap_to_disconnect">disconnect</string>
    <!-- QuickSettings: Accessibility label to activate a device [CHAR LIMIT=NONE]-->
    <string name="accessibility_quick_settings_bluetooth_device_tap_to_activate">activate</string>
    <!-- QuickSettings: Bluetooth auto on tomorrow [CHAR LIMIT=NONE]-->
    <string name="turn_on_bluetooth_auto_tomorrow">Automatically turn on tomorrow</string>
    <!-- QuickSettings: Bluetooth auto on info text when disabled [CHAR LIMIT=NONE]-->
    <string name="turn_on_bluetooth_auto_info_disabled">Features like Quick Share and Find My Device use Bluetooth</string>
    <!-- QuickSettings: Bluetooth auto on info text when enabled [CHAR LIMIT=NONE]-->
    <string name="turn_on_bluetooth_auto_info_enabled">Bluetooth will turn on tomorrow morning</string>
    <!-- QuickSettings: Bluetooth dialog audio sharing button text [CHAR LIMIT=50]-->
    <string name="quick_settings_bluetooth_audio_sharing_button">Share audio</string>
    <!-- QuickSettings: Bluetooth dialog audio sharing button text when sharing audio [CHAR LIMIT=50]-->
    <string name="quick_settings_bluetooth_audio_sharing_button_sharing">Sharing audio</string>

    <!-- QuickSettings: Bluetooth secondary label for the battery level of a connected device [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_battery_level"><xliff:g id="battery_level_as_percentage">%s</xliff:g> battery</string>
    <!-- QuickSettings: Bluetooth secondary label for an audio device being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_audio">Audio</string>
    <!-- QuickSettings: Bluetooth secondary label for a headset being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_headset">Headset</string>
    <!-- QuickSettings: Bluetooth secondary label for an input/IO device being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_input">Input</string>
    <!-- QuickSettings: Bluetooth secondary label for a Hearing aids device being connected [CHAR LIMIT=20]-->
    <string name="quick_settings_bluetooth_secondary_label_hearing_aids">Hearing aids</string>
    <!-- QuickSettings: Bluetooth secondary label shown when bluetooth is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_bluetooth_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Brightness [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Rotation Unlocked [CHAR LIMIT=NONE] -->
    <string name="quick_settings_rotation_unlocked_label">Auto-rotate</string>
    <!-- Accessibility label for Auto-ratate QuickSettings tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_rotation">Auto-rotate screen</string>
    <!-- Accessibility label for value of Auto-ratate QuickSettings tile [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Rotation Locked [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Locked to Portrait [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Locked to Landscape [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: IME [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Location [CHAR LIMIT=NONE] -->
    <string name="quick_settings_location_label">Location</string>
    <!-- QuickSettings: Location (Off) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Screen saver [CHAR LIMIT=NONE] -->
    <string name="quick_settings_screensaver_label">Screen saver</string>
    <!-- QuickSettings: Camera [CHAR LIMIT=NONE] -->
    <string name="quick_settings_camera_label">Camera access</string>
    <!-- QuickSettings: Microphone [CHAR LIMIT=NONE] -->
    <string name="quick_settings_mic_label">Mic access</string>
    <!-- QuickSettings: Camera or microphone access is allowed. If you update this string, please
    update the string "available" in packages/modules/Permission/PermissionController[CHAR LIMIT=NONE] -->
    <string name="quick_settings_camera_mic_available">Available</string>
    <!-- QuickSettings: Camera or microphone access is blocked. If you update this string, please
    update the string "blocked" in packages/modules/Permission/PermissionController [CHAR LIMIT=NONE] -->
    <string name="quick_settings_camera_mic_blocked">Blocked</string>
    <!-- QuickSettings: Media device [CHAR LIMIT=NONE] -->
    <string name="quick_settings_media_device_label">Media device</string>
    <!-- QuickSettings: RSSI [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: RSSI (No network) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Settings [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Time [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: User [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Title of the user detail panel [CHAR LIMIT=NONE] -->
    <string name="quick_settings_user_title">User</string>
    <!-- QuickSettings: Label on the item for adding a new user [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Wifi [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_label">Wi-Fi</string>
    <!-- QuickSettings: Internet [CHAR LIMIT=NONE] -->
    <string name="quick_settings_internet_label">Internet</string>
    <!-- QuickSettings: networks available [CHAR LIMIT=NONE] -->
    <string name="quick_settings_networks_available">Networks available</string>
    <!-- QuickSettings: networks unavailable [CHAR LIMIT=NONE] -->
    <string name="quick_settings_networks_unavailable">Networks unavailable</string>
    <!-- QuickSettings: Wifi (Not connected) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Wifi (No network) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Wifi (Off) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Wifi (On) [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Wifi detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_detail_empty_text">No Wi-Fi networks available</string>
    <!-- QuickSettings: Wifi secondary label shown when the wifi is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_wifi_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Cast title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_title">Cast</string>
    <!-- QuickSettings: Cast detail panel, status text when casting [CHAR LIMIT=NONE] -->
    <string name="quick_settings_casting">Casting</string>
    <!-- QuickSettings: Cast detail panel, default device name [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_device_default_name">Unnamed device</string>
     <!-- QuickSettings: Cast detail panel, default device description [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Cast detail panel, text when there are no items [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_detail_empty_text">No devices available</string>
    <!-- QuickSettings: Cast unavailable, text when not connected to WiFi or ethernet[CHAR LIMIT=NONE] -->
    <string name="quick_settings_cast_no_network">No Wi\u2011Fi or Ethernet connection</string>
    <!-- QuickSettings: Brightness dialog title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_brightness_dialog_title">Brightness</string>
    <!-- QuickSettings: Label for the toggle that controls whether display inversion is enabled. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_inversion_label">Color inversion</string>
    <!-- QuickSettings: Label for the toggle that controls whether display color correction is enabled. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_color_correction_label">Color correction</string>
    <!-- QuickSettings: Label for font size scaling. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_font_scaling_label">Font size</string>
    <!-- QuickSettings: Control panel: Label for button that navigates to user settings. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_more_user_settings">Manage users</string>
    <!-- QuickSettings: Control panel: Label for button that dismisses control panel. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_done">Done</string>
    <!-- QuickSettings: Control panel: Label for button that dismisses user switcher control panel. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_close_user_panel">Close</string>
    <!-- QuickSettings: Control panel: Label for connected device. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connected">Connected</string>
    <!-- QuickSettings: Control panel: Label for connected device, showing remote device battery level. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connected_battery_level">Connected, battery <xliff:g id="battery_level_as_percentage">%1$s</xliff:g></string>
    <!-- QuickSettings: Control panel: Label for connecting device. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_connecting">Connecting...</string>
    <!-- QuickSettings: Tethering. [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Hotspot. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hotspot_label">Hotspot</string>
    <!-- QuickSettings: Hotspot. Secondary label shown when the hotspot is being enabled [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hotspot_secondary_label_transient">Turning on&#8230;</string>
    <!-- QuickSettings: Hotspot. Secondary label shown when Data Saver mode is enabled to explain to
         the user why they can't toggle the hotspot tile. [CHAR LIMIT=20] -->
    <string name="quick_settings_hotspot_secondary_label_data_saver_enabled">Data Saver is on</string>
    <!-- QuickSettings: Hotspot: Secondary label for how many devices are connected to the hotspot [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hotspot_secondary_label_num_devices">{count, plural,
        =1 {# device}
        other {# devices}
    }</string>
    <!-- QuickSettings: Notifications [CHAR LIMIT=NONE] -->
    <!-- QuickSettings: Flashlight [CHAR LIMIT=NONE] -->
    <string name="quick_settings_flashlight_label">Flashlight</string>
    <!-- QuickSettings: Flashlight, used when it's not available due to camera in use [CHAR LIMIT=NONE] -->
    <string name="quick_settings_flashlight_camera_in_use">Camera in use</string>
    <!-- QuickSettings: Cellular detail panel title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_title">Mobile data</string>
    <!-- QuickSettings: Cellular detail panel, data usage title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_usage">Data usage</string>
    <!-- QuickSettings: Cellular detail panel, remaining data title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_remaining_data">Remaining data</string>
    <!-- QuickSettings: Cellular detail panel, over limit title [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_over_limit">Over limit</string>
    <!-- QuickSettings: Cellular detail panel, data used format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_used"><xliff:g id="data_used" example="2.0 GB">%s</xliff:g> used</string>
    <!-- QuickSettings: Cellular detail panel, data limit format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_limit"><xliff:g id="data_limit" example="2.0 GB">%s</xliff:g> limit</string>
    <!-- QuickSettings: Cellular detail panel, data warning format string [CHAR LIMIT=NONE] -->
    <string name="quick_settings_cellular_detail_data_warning"><xliff:g id="data_limit" example="2.0 GB">%s</xliff:g> warning</string>
    <!-- QuickSettings: This string is in the easy-to-view settings that a user can pull down from
    the top of their phone's screen. This is a label for a toggle to turn the work profile on and
    off. This means a separate profile on a user's phone that's specifically for their
    work apps and managed by their company. "Work" is used as an adjective. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_work_mode_label">Work apps</string>
    <!-- QuickSettings: Subtitle for the Work profile Quick Settings tile when it's in the off
    state. This corresponds to the work profile not being currently accessible. [CHAR LIMIT=20] -->
    <string name="quick_settings_work_mode_paused_state">Paused</string>
    <!-- QuickSettings: Label for the toggle to activate Night display (renamed "Night Light" with title caps). [CHAR LIMIT=20] -->
    <!--redmine 319619 modify Night Light to Eye comfort by gww 20250319 begin-->
    <string name="quick_settings_night_display_label">Eye Comfort</string>
    <!--redmine 319619 modify Night Light to Eye comfort by gww 20250319 end-->
    <!-- QuickSettings: Secondary text for when the Night Light will be enabled at sunset. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_on_at_sunset">On at sunset</string>
    <!-- QuickSettings: Secondary text for when the Night Light will be on until sunrise. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_until_sunrise">Until sunrise</string>
    <!-- QuickSettings: Secondary text for when the Night Light will be enabled at some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_night_secondary_label_on_at">On at <xliff:g id="time" example="10 pm">%s</xliff:g></string>
    <!-- QuickSettings: Secondary text for when the Night Light or some other tile will be on until some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_secondary_label_until">Until <xliff:g id="time" example="7 am">%s</xliff:g></string>
    <!-- QuickSettings: Label for the toggle to activate dark theme (A.K.A Dark Mode). [CHAR LIMIT=20] -->
    <string name="quick_settings_ui_mode_night_label">Dark theme</string>
    <!-- QuickSettings: Secondary text for the dark theme tile when enabled by battery saver. [CHAR LIMIT=20] -->
    <string name="quick_settings_dark_mode_secondary_label_battery_saver">Battery Saver</string>
    <!-- QuickSettings: Secondary text for when the Dark Mode will be enabled at sunset. [CHAR LIMIT=20] -->
    <string name="quick_settings_dark_mode_secondary_label_on_at_sunset">On at sunset</string>
    <!-- QuickSettings: Secondary text for when the Dark Mode will be on until sunrise. [CHAR LIMIT=20] -->
    <string name="quick_settings_dark_mode_secondary_label_until_sunrise">Until sunrise</string>
    <!-- QuickSettings: Secondary text for when the Dark theme will be enabled at some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_dark_mode_secondary_label_on_at">On at <xliff:g id="time" example="10 pm">%s</xliff:g></string>
    <!-- QuickSettings: Secondary text for when the Dark theme or some other tile will be on until some user-selected time. [CHAR LIMIT=20] -->
    <string name="quick_settings_dark_mode_secondary_label_until">Until <xliff:g id="time" example="7 am">%s</xliff:g></string>
    <!-- QuickSettings: Secondary text for when the Dark theme will be enabled at bedtime. [CHAR LIMIT=40] -->
    <string name="quick_settings_dark_mode_secondary_label_on_at_bedtime">On at bedtime</string>
    <!-- QuickSettings: Secondary text for when the Dark theme or some other tile will be on until bedtime ends. [CHAR LIMIT=40] -->
    <string name="quick_settings_dark_mode_secondary_label_until_bedtime_ends">Until bedtime ends</string>

    <!-- QuickSettings: NFC tile [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_label">NFC</string>
    <!-- QuickSettings: NFC (off) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_off">NFC is disabled</string>
    <!-- QuickSettings: NFC (on) [CHAR LIMIT=NONE] -->
    <string name="quick_settings_nfc_on">NFC is enabled</string>

    <!-- QuickSettings: Screen record tile [CHAR LIMIT=NONE] -->
    <string name="quick_settings_screen_record_label">Screen record</string>
    <!-- QuickSettings: Text to prompt the user to begin a new recording [CHAR LIMIT=20] -->
    <string name="quick_settings_screen_record_start">Start</string>
    <!-- QuickSettings: Text to prompt the user to stop an ongoing recording [CHAR LIMIT=20] -->
    <string name="quick_settings_screen_record_stop">Stop</string>

    <!-- QuickSettings: Record Issue tile [CHAR LIMIT=NONE] -->
    <string name="qs_record_issue_label">Record Issue</string>
    <!-- QuickSettings: Text to prompt the user to begin a new recording [CHAR LIMIT=20] -->
    <string name="qs_record_issue_start">Start</string>
    <!-- QuickSettings: Text to prompt the user to stop an ongoing recording [CHAR LIMIT=20] -->
    <string name="qs_record_issue_stop">Stop</string>
    <!-- QuickSettings: Should user take a bugreport or only share trace files [CHAR LIMIT=20] -->
    <string name="qs_record_issue_bug_report">Bug Report</string>

    <!-- QuickSettings: Issue Type Drop down options in Record Issue Start Dialog [CHAR LIMIT=50] -->
    <string name="qs_record_issue_dropdown_header">What part of your device experience was affected?</string>
    <!-- QuickSettings: Issue Type Drop down prompt in Record Issue Start Dialog [CHAR LIMIT=30] -->
    <string name="qs_record_issue_dropdown_prompt">Select issue type</string>
    <!-- QuickSettings: Screen record switch label in Record Issue Start Dialog [CHAR LIMIT=20] -->
    <string name="qs_record_issue_dropdown_screenrecord">Screen record</string>

    <!-- QuickSettings: Issue Type Drop down choices list in Record Issue Start Dialog [CHAR LIMIT=30] -->
    <string name="performance">Performance</string>
    <string name="user_interface">User Interface</string>
    <string name="thermal">Thermal</string>

    <!-- QuickSettings: Label for the toggle that controls whether One-handed mode is enabled. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_onehanded_label">One-handed mode</string>

    <!-- Hearing devices -->
    <!-- QuickSettings: Hearing devices [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hearing_devices_label">Hearing devices</string>
    <!-- QuickSettings: Hearing Devices' secondary label shown when the hearing aids are currently connected. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hearing_devices_connected">Active</string>
    <!-- QuickSettings: Hearing devices' secondary label shown when there is no connected hearing aids. [CHAR LIMIT=NONE] -->
    <string name="quick_settings_hearing_devices_disconnected">Disconnected</string>
    <!-- QuickSettings: Quick Settings Hearing devices dialog title [CHAR LIMIT=30] -->
    <string name="quick_settings_hearing_devices_dialog_title">Hearing devices</string>
    <!-- QuickSettings: Hearing devices dialog pair new device [CHAR LIMIT=NONE]-->
    <string name="quick_settings_pair_hearing_devices">Pair new device</string>
    <!-- QuickSettings: Content description of the hearing devices dialog pair new device [CHAR LIMIT=NONE] -->
    <string name="accessibility_hearing_device_pair_new_device">Click to pair new device</string>
    <!-- QuickSettings: Message when selecting hearing aids presets failed. [CHAR LIMIT=NONE] -->
    <string name="hearing_devices_presets_error">Couldn\'t update preset</string>
    <!-- QuickSettings: Title for hearing aids presets. Preset is a set of hearing aid settings. User can apply different settings in different environments (e.g. Outdoor, Restaurant, Home) [CHAR LIMIT=40]-->
    <string name="hearing_devices_preset_label">Preset</string>
    <!-- QuickSettings: Tool name for hearing devices dialog related tools [CHAR LIMIT=40]-->
    <string name="live_caption_title">Live Caption</string>

    <!--- Title of dialog triggered if the microphone is disabled but an app tried to access it. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_mic_dialog_title">Unblock device microphone?</string>
    <!--- Title of dialog triggered if the camera is disabled but an app tried to access it. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_camera_dialog_title">Unblock device camera?</string>
    <!--- Title of dialog triggered if both microphone and camera are disabled but an app tried to access them. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_mic_camera_dialog_title">Unblock device camera and microphone?</string>

    <!--- Content of dialog triggered if the microphone is disabled but an app tried to access it. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_start_use_mic_dialog_content">This unblocks access for all apps and services allowed to use your microphone.</string>
    <!--- Content of dialog triggered if the camera is disabled but an app tried to access it. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_start_use_camera_dialog_content">This unblocks access for all apps and services allowed to use your camera.</string>
    <!--- Title of dialog triggered if both microphone and camera are disabled but an app tried to access them. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_mic_camera_dialog_content">This unblocks access for all apps and services allowed to use your camera or microphone.</string>

    <!--- Title of dialog triggered if the microphone is blocked by a hardware privacy switch but an app tried to access it. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_mic_blocked_dialog_title">Microphone is blocked</string>
    <!--- Title of dialog triggered if the camera is blocked by a hardware privacy switch but an app tried to access it. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_camera_blocked_dialog_title">Camera is blocked</string>
    <!--- Title of dialog triggered if both microphone and camera are blocked by a hardware privacy switch but an app tried to access them. [CHAR LIMIT=150] -->
    <string name="sensor_privacy_start_use_mic_camera_blocked_dialog_title">The mic &amp; camera are blocked</string>

    <!--- Content of dialog triggered if the microphone is disabled by HW toggle but an app tried to access it. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_start_use_mic_blocked_dialog_content">
        To unblock, move the privacy switch on your device to the microphone on position to allow
        microphone access. Refer to the device manual to locate the privacy switch on your device.
    </string>
    <!--- Content of dialog triggered if the camera is disabled by HW toggle but an app tried to access it. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_start_use_camera_blocked_dialog_content">
        To unblock, move the privacy switch on your device to the camera on position to allow camera
        access. Refer to the device manual to locate the privacy switch on your device.
    </string>
    <!--- Content of dialog triggered if the camera is disabled by HW toggle but an app tried to access it. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_start_use_mic_camera_blocked_dialog_content">
        To unblock them, move the privacy switch on your device to the unblocked position to allow
        access. Refer to the device manual to locate the privacy switch on your device.
    </string>

    <!--- Content of toast triggered if the microphone privacy is unblocked while the HW toggle privacy dialog was shown. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_unblocked_toast_content">
        Microphone available
    </string>
    <!--- Content of toast triggered if the camera privacy is unblocked while the HW toggle privacy dialog was shown. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_camera_unblocked_toast_content">
        Camera available
    </string>
    <!--- Content of toast triggered if both mic and camera privacy is unblocked while the HW toggle privacy dialog was shown. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_camera_unblocked_toast_content">
        Microphone and camera available
    </string>

    <!--- Title of dialog triggered if the microphone sensor privacy changed its state to unblocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_turned_on_dialog_title">
        Microphone turned on
    </string>

    <!--- Title of dialog triggered if the microphone sensor privacy changed its state to blocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_turned_off_dialog_title">
        Microphone turned off
    </string>

    <!--- Content of dialog triggered if the microphone sensor privacy changed its state to unblocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_unblocked_dialog_content">
        Microphone is enabled for all apps and services.
    </string>

    <!--- Content of dialog triggered if the microphone sensor privacy changed its state to blocked.
        and there are no active exceptions for explicit user interaction [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_blocked_no_exception_dialog_content">
        Microphone access is disabled for all apps and services.
        You can enable microphone access in Settings &gt; Privacy &gt; Microphone.
    </string>

    <!--- Content of dialog triggered if the microphone sensor privacy changed its state to blocked.
        and there are active exceptions for explicit user interaction [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_mic_blocked_with_exception_dialog_content">
        Microphone access is disabled for all apps and services.
        You can change this in Settings &gt; Privacy &gt; Microphone.
    </string>

    <!--- Title of dialog triggered if the camera sensor privacy changed its state to unblocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_camera_turned_on_dialog_title">
        Camera turned on
    </string>

    <!--- Title of dialog triggered if the camera sensor privacy changed its state to blocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_camera_turned_off_dialog_title">
        Camera turned off
    </string>

    <!--- Content of dialog triggered if the camera sensor privacy changed its state to unblocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_camera_unblocked_dialog_content">
        Camera is enabled for all apps and services.
    </string>

    <!--- Content of dialog triggered if the camera sensor privacy changed its state to blocked. [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_camera_blocked_dialog_content">
        Camera access is disabled for all apps and services.
    </string>

    <!--- Content of dialog triggered if the microphone sensor privacy changed its state to blocked.
        and there are active exceptions for explicit user interaction [CHAR LIMIT=NONE] -->
    <string name="sensor_privacy_htt_blocked_dialog_content">To use the microphone button, enable microphone access in Settings.</string>

    <!-- Sensor privacy dialog: Button to open system settings [CHAR LIMIT=50] -->
    <string name="sensor_privacy_dialog_open_settings">Open Settings</string>

    <!-- Default name for the media device shown in the output switcher when the name is not available [CHAR LIMIT=30] -->
    <string name="media_seamless_other_device">Other device</string>

    <!-- QuickStep: Accessibility to toggle overview [CHAR LIMIT=40] -->
    <string name="quick_step_accessibility_toggle_overview">Toggle Overview</string>

    <!-- Zen mode: Priority only introduction message on first use -->
    <string name="zen_priority_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms, reminders, events, and callers you specify. You\'ll still hear anything you choose to play including music, videos, and games.</string>

    <!-- Zen mode: Alarms only introduction message on first use -->
    <string name="zen_alarms_introduction">You won\'t be disturbed by sounds and vibrations, except from alarms. You\'ll still hear anything you choose to play including music, videos, and games.</string>

    <!-- Zen mode: Priority only customization button label -->
    <string name="zen_priority_customize_button">Customize</string>

    <!-- Zen mode: Total silence introduction message on first use (voice capable devices) -->
    <string name="zen_silence_introduction_voice">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games. You\'ll still be able to make phone calls.</string>

    <!-- Zen mode: Total silence introduction message on first use (non-voice capable devices) -->
    <string name="zen_silence_introduction">This blocks ALL sounds and vibrations, including from alarms, music, videos, and games.</string>

    <!-- Shows to explain the double tap interaction with notifications: After tapping a notification on Keyguard, this will explain users to tap again to launch a notification. [CHAR LIMIT=60] -->
    <string name="notification_tap_again">Tap again to open</string>

    <!-- Asks for a second tap as confirmation on an item that normally requires one tap. [CHAR LIMIT=60] -->
    <string name="tap_again">Tap again</string>

    <!-- Message shown when lock screen is tapped or face authentication fails. [CHAR LIMIT=60] -->
    <string name="keyguard_unlock">Swipe up to open</string>

    <!-- Message shown when lock screen is unlocked (ie: by trust agent or face auth). Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_unlock_press">Press the unlock icon to open</string>

    <!-- Message shown when non-bypass face authentication succeeds. Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_swipe">Unlocked by face. Swipe up to open.</string>
    <!-- Message shown when non-bypass face authentication succeeds and UDFPS is supported. Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_press">Unlocked by face. Press the unlock icon to open.</string>
    <!-- Message shown when non-bypass face authentication succeeds and UDFPS is supported. Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_press_alt_1">Unlocked by face. Press to open.</string>
    <!-- Message shown when non-bypass face authentication succeeds and UDFPS is supported. Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_press_alt_2">Face recognized. Press to open.</string>
    <!-- Message shown when non-bypass face authentication succeeds and UDFPS is supported. Provides extra instructions for how the user can enter their device [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_press_alt_3">Face recognized. Press the unlock icon to open.</string>

    <!-- Message shown when non-bypass face authentication succeeds. [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock">Unlocked by face</string>
    <!-- Message shown when non-bypass face authentication succeeds. [CHAR LIMIT=60] -->
    <string name="keyguard_face_successful_unlock_alt1">Face recognized</string>

    <!-- Message shown when face authentication fails and the pin pad is visible. [CHAR LIMIT=60] -->
    <string name="keyguard_retry">Swipe up to try again</string>
    <!-- Message shown when face authentication fails and the pin pad is visible. [CHAR LIMIT=60] -->
    <string name="accesssibility_keyguard_retry">Swipe up to try Face Unlock again</string>

    <!-- Message shown when notifying user to unlock in order to use NFC. [CHAR LIMIT=60] -->
    <string name="require_unlock_for_nfc">Unlock to use NFC</string>

    <!-- Text on keyguard screen and in Quick Settings footer indicating that the user's device belongs to their organization. [CHAR LIMIT=60] -->
    <string name="do_disclosure_generic">This device belongs to your organization</string>

    <!-- Text on keyguard screen and in Quick Settings footer indicating that user's device belongs to their organization. [CHAR LIMIT=40] -->
    <string name="do_disclosure_with_name">This device belongs to <xliff:g id="organization_name" example="Foo, Inc.">%s</xliff:g></string>

    <!-- Text on keyguard screen and in Quick Settings footer indicating that the user's device is provided by the Creditor. [CHAR LIMIT=60] -->
    <string name="do_financed_disclosure_with_name">This device is provided by <xliff:g id="organization_name" example="Foo, Inc.">%s</xliff:g></string>

    <!-- Shows when people have clicked on the phone icon [CHAR LIMIT=60] -->
    <string name="phone_hint">Swipe from icon for phone</string>

    <!-- Shows when people have clicked on the voice assist icon [CHAR LIMIT=60] -->
    <string name="voice_hint">Swipe from icon for voice assist</string>

    <!-- Shows when people have clicked on the camera icon [CHAR LIMIT=60] -->
    <string name="camera_hint">Swipe from icon for camera</string>

    <!-- Accessibility content description for Interruption level: None. [CHAR LIMIT=NONE] -->
    <string name="interruption_level_none_with_warning">Total silence.  This will also silence screen readers.</string>

    <!-- Interruption level: None. [CHAR LIMIT=40] -->
    <string name="interruption_level_none">Total silence</string>

    <!-- Interruption level: Priority. [CHAR LIMIT=40] -->
    <string name="interruption_level_priority">Priority only</string>

    <!-- Interruption level: Alarms only. [CHAR LIMIT=40] -->
    <string name="interruption_level_alarms">Alarms only</string>

    <!-- Interruption level: None.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_none_twoline">Total\nsilence</string>

    <!-- Interruption level: Priority.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_priority_twoline">Priority\nonly</string>

    <!-- Interruption level: Alarms only.  Optimized for narrow two-line display. [CHAR LIMIT=40] -->
    <string name="interruption_level_alarms_twoline">Alarms\nonly</string>

    <!-- Indication on the keyguard that is shown when the device is wirelessly charging. [CHAR LIMIT=80]-->
    <string name="keyguard_indication_charging_time_wireless"><xliff:g id="percentage" example="20%">%2$s</xliff:g> • Charging wirelessly • Full in <xliff:g id="charging_time_left" example="4 hr, 2 min">%1$s</xliff:g></string>

    <!-- Indication on the keyguard that is shown when the device is charging. [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time"><xliff:g id="percentage">%2$s</xliff:g> • Charging • Full in <xliff:g id="charging_time_left" example="4 hr, 2 min">%1$s</xliff:g></string>

    <!-- Indication on the keyguard that is shown when the device is charging rapidly. Should match keyguard_plugged_in_charging_fast [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time_fast"><xliff:g id="percentage">%2$s</xliff:g> • Charging rapidly • Full in <xliff:g id="charging_time_left" example="4 hr, 2 min">%1$s</xliff:g></string>

    <!-- Indication on the keyguard that is shown when the device is charging slowly. Should match keyguard_plugged_in_charging_slowly [CHAR LIMIT=50]-->
    <string name="keyguard_indication_charging_time_slowly"><xliff:g id="percentage">%2$s</xliff:g> • Charging slowly • Full in <xliff:g id="charging_time_left" example="4 hr, 2 min">%1$s</xliff:g></string>

    <!-- Indication on the keyguard that is shown when the device is dock charging. [CHAR LIMIT=80]-->
    <string name="keyguard_indication_charging_time_dock"><xliff:g id="percentage" example="20%">%2$s</xliff:g> • Charging • Full in <xliff:g id="charging_time_left" example="4 hr, 2 min">%1$s</xliff:g></string>

    <!-- Label for accessibility action that shows widgets on lock screen on click. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_open_communal_hub">Widgets on lock screen</string>

    <!-- Indicator on keyguard to start the communal tutorial. [CHAR LIMIT=100] -->
    <string name="communal_tutorial_indicator_text">Swipe left to start the communal tutorial</string>

    <!-- Text for call-to-action button that launches the hub mode widget editor on click. [CHAR LIMIT=50] -->
    <string name="cta_tile_button_to_open_widget_editor">Customize</string>
    <!-- Text for call-to-action button that dismisses the tile on click. [CHAR LIMIT=50] -->
    <string name="cta_tile_button_to_dismiss">Dismiss</string>
    <!-- Label for call-to-action tile to edit the glanceable hub [CHAR LIMIT=100] -->
    <string name="cta_label_to_edit_widget">Add, remove, and reorder your widgets in this space</string>
    <!-- Label for call-to-action tile that opens widget picker on click in edit mode [CHAR LIMIT=50] -->
    <string name="cta_label_to_open_widget_picker">Add more widgets</string>
    <!-- Text for the popup to be displayed after dismissing the call-to-action tile. [CHAR LIMIT=50] -->
    <string name="popup_on_dismiss_cta_tile_text">Long press to customize widgets</string>
    <!-- Text for the button to configure widgets after long press. [CHAR LIMIT=50] -->
    <string name="button_to_configure_widgets_text">Customize widgets</string>
    <!-- Description for the App icon of disabled widget. [CHAR LIMIT=NONE] -->
    <string name="icon_description_for_disabled_widget">App icon for disabled widget</string>
    <!-- Description for the App icon of a package that is currently being installed. [CHAR LIMIT=NONE] -->
    <string name="icon_description_for_pending_widget">App icon for a widget being installed</string>
    <!-- Label for the button which configures widgets [CHAR LIMIT=NONE] -->
    <string name="edit_widget">Edit widget</string>
    <!-- Description for the button that removes a widget on click. [CHAR LIMIT=50] -->
    <string name="button_to_remove_widget">Remove</string>
    <!-- Text for the button that launches the hub mode widget picker. [CHAR LIMIT=50] -->
    <string name="hub_mode_add_widget_button_text">Add widget</string>
    <!-- Text for the button that exits the hub mode editing mode. [CHAR LIMIT=50] -->
    <string name="hub_mode_editing_exit_button_text">Done</string>
    <!-- Label for the button in the empty state call-to-action tile that will open the widget picker. [CHAR LIMIT=NONE] -->
    <string name="label_for_button_in_empty_state_cta">Add widgets</string>
    <!-- Title for the empty state call-to-action when no widgets are available in the hub. [CHAR LIMIT=NONE] -->
    <string name="title_for_empty_state_cta">Get quick access to your favorite app widgets without unlocking your tablet.</string>
    <!-- Title for the dialog that redirects users to change allowed widget category in settings. [CHAR LIMIT=NONE] -->
    <string name="dialog_title_to_allow_any_widget">Allow any widget on lock screen?</string>
    <!-- Text for the button in the dialog that opens when tapping on disabled widgets. [CHAR LIMIT=NONE] -->
    <string name="button_text_to_open_settings">Open settings</string>
    <!-- Title of a dialog. This text is confirming that the user wants to turn on access to their work apps, which the user had previously paused. "Work" is an adjective. [CHAR LIMIT=30] -->
    <string name="work_mode_off_title">Unpause work apps?</string>
    <!-- Title for button to unpause on work profile. [CHAR LIMIT=NONE] -->
    <string name="work_mode_turn_on">Unpause</string>
    <!-- Label for accessibility action that navigates to lock screen. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_label_close_communal_hub">Close widgets on lock screen</string>
    <!-- Label for accessibility action that navigates to edit mode. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_label_edit_widgets">Customize widgets</string>
    <!-- Accessibility content description for communal hub. [CHAR LIMIT=NONE] -->
    <string name="accessibility_content_description_for_communal_hub">Widgets on lock screen</string>
    <!-- Label for accessibility action to select a widget in edit mode. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_label_select_widget">select widget</string>
    <!-- Label for accessibility action to remove a widget in edit mode. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_label_remove_widget">remove widget</string>
    <!-- Label for accessibility action to place a widget in edit mode after selecting move widget. [CHAR LIMIT=NONE] -->
    <string name="accessibility_action_label_place_widget">place selected widget</string>

    <!-- Related to user switcher --><skip/>

    <!-- Accessibility label for the button that opens the user switcher. -->
    <string name="accessibility_multi_user_switch_switcher">Switch user</string>

    <!-- Accessibility role description for the element that opens the user switcher list. -->
    <string name="accessibility_multi_user_list_switcher">pulldown menu</string>

    <!-- Accessibility label for the user icon on the lock screen. -->

    <!-- Accessibility label for the button that opens the quick contact of the user. -->

    <!-- Message of the confirmation dialog when exiting guest session [CHAR LIMIT=NONE] -->
    <string name="guest_exit_guest_dialog_message">All apps and data in this session will be deleted.</string>

    <!-- Title of the notification when resuming an existing guest session [CHAR LIMIT=NONE] -->
    <string name="guest_wipe_session_title">Welcome back, guest!</string>

    <!-- Message of the notification when resuming an existing guest session [CHAR LIMIT=NONE] -->
    <string name="guest_wipe_session_message">Do you want to continue your session?</string>

    <!-- Notification when resuming an existing guest session: Action that starts a new session [CHAR LIMIT=35] -->
    <string name="guest_wipe_session_wipe">Start over</string>

    <!-- Notification when resuming an existing guest session: Action that continues with the current session [CHAR LIMIT=35] -->
    <string name="guest_wipe_session_dontwipe">Yes, continue</string>

    <!-- App name of the notification when guest mode is entered [CHAR LIMIT=35] -->
    <string name="guest_notification_app_name">Guest mode</string>
    <!-- Title of the notification when guest mode is entered [CHAR LIMIT=35] -->
    <string name="guest_notification_session_active">You are in guest mode</string>

    <!-- Additional message for add user confirmation dialog that is appended when current user is
         guest and guest is ephemeral. This is to warn users that current guest session
         would get removed after a new user is added and switched to [CHAR LIMIT=none] -->
    <string name="user_add_user_message_guest_remove">\n\nAdding a new user will exit guest mode
        and delete all apps and data from the current guest session.</string>

    <!-- Title for the dialog that lets users know that the maximum allowed number of users on the device has been reached. [CHAR LIMIT=35]-->
    <string name="user_limit_reached_title">User limit reached</string>

    <!-- Message that tells people what's the maximum number of uses allowed on the device. [CHAR_LIMIT=NONE]-->
    <string name="user_limit_reached_message">{count, plural,
        =1 {Only one user can be created.}
        other {You can add up to # users.}
    }</string>

    <!-- Title of the confirmation dialog for deleting a user [CHAR LIMIT=NONE] -->
    <string name="user_remove_user_title">Remove user?</string>

    <!-- Message of the confirmation dialog for deleting a user [CHAR LIMIT=NONE] -->
    <string name="user_remove_user_message">All apps and data of this user will be deleted.</string>

    <!-- Label for button in confirmation dialog when exiting guest session [CHAR LIMIT=35] -->
    <string name="user_remove_user_remove">Remove</string>

    <!-- Media projection without Partial Screenshare -->
    <!-- Media projection permission dialog warning title. [CHAR LIMIT=NONE] -->
    <string name="media_projection_dialog_title">Start recording or casting with <xliff:g id="app_seeking_permission" example="Hangouts">%s</xliff:g>?</string>
    <!-- Media projection permission dialog warning text. [CHAR LIMIT=NONE] -->
    <string name="media_projection_dialog_warning"><xliff:g id="app_seeking_permission" example="Hangouts">%s</xliff:g> will have access to all of the information that is visible on your screen or played from your device while recording or casting. This includes information such as passwords, payment details, photos, messages, and audio that you play.</string>
    <!-- Media projection permission dialog warning title for system services. [CHAR LIMIT=NONE] -->
    <string name="media_projection_sys_service_dialog_title">Start recording or casting?</string>
    <!-- Media projection permission dialog warning text for system services. [CHAR LIMIT=NONE] -->
    <string name="media_projection_sys_service_dialog_warning">The service providing this function will have access to all of the information that is visible on your screen or played from your device while recording or casting. This includes information such as passwords, payment details, photos, messages, and audio that you play.</string>

    <!-- Permission dropdown option for sharing or recording the whole screen. [CHAR LIMIT=30] -->
    <string name="screen_share_permission_dialog_option_entire_screen">Entire screen</string>
    <!-- Permission dropdown option for sharing or recording single app. [CHAR LIMIT=30] -->
    <string name="screen_share_permission_dialog_option_single_app">A single app</string>
    <!-- Title of the dialog that allows to select an app to share or record [CHAR LIMIT=NONE] -->
    <string name="screen_share_permission_app_selector_title">Share or record an app</string>

    <!-- Media projection that launched from 1P/3P apps -->
    <!-- 1P/3P app media projection permission dialog title. [CHAR LIMIT=NONE] -->
    <string name="media_projection_entry_app_permission_dialog_title">Start recording or casting with <xliff:g id="app_seeking_permission" example="Meet">%s</xliff:g>?</string>
    <!-- 1P/3P app media projection permission warning for capturing the whole screen. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_app_permission_dialog_warning_entire_screen">When you’re sharing, recording, or casting, <xliff:g id="app_seeking_permission" example="Meet">%s</xliff:g> has access to anything visible on your screen or played on your device. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- 1P/3P app media projection permission warning for capturing an app. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_app_permission_dialog_warning_single_app">When you’re sharing, recording, or casting an app, <xliff:g id="app_seeking_permission" example="Meet">%s</xliff:g> has access to anything shown or played on that app. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- 1P/3P apps media projection permission button to continue with app selection or recording [CHAR LIMIT=60] -->
    <string name="media_projection_entry_app_permission_dialog_continue">Start</string>
    <!-- 1P/3P apps disabled the single app projection option. [CHAR LIMIT=NONE] -->
    <string name="media_projection_entry_app_permission_dialog_single_app_disabled"><xliff:g id="app_name" example="Meet">%1$s</xliff:g> has disabled this option</string>

    <!-- Casting that launched by SysUI (i.e. when there is no app name) -->
    <!-- System casting media projection permission dialog title. [CHAR LIMIT=100] -->
    <string name="media_projection_entry_cast_permission_dialog_title">Start casting?</string>
    <!-- System casting media projection permission warning for capturing the whole screen when SysUI casting requests it. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_cast_permission_dialog_warning_entire_screen">When you’re casting, Android has access to anything visible on your screen or played on your device. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- System casting media projection permission warning for capturing a single app when SysUI casting requests it. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_cast_permission_dialog_warning_single_app">When you’re casting an app, Android has access to anything shown or played on that app. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- System casting media projection permission button to continue for SysUI casting. [CHAR LIMIT=60] -->
    <string name="media_projection_entry_cast_permission_dialog_continue">Start casting</string>

    <!-- Other sharing (not recording nor casting) that launched by SysUI (currently not in use) -->
    <!-- System sharing media projection permission dialog title. [CHAR LIMIT=100] -->
    <string name="media_projection_entry_generic_permission_dialog_title">Start sharing?</string>
    <!-- System sharing media projection permission warning for capturing the whole screen. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_generic_permission_dialog_warning_entire_screen">When you’re sharing, recording, or casting, Android has access to anything visible on your screen or played on your device. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- System sharing media projection permission warning for capturing a single app. [CHAR LIMIT=350] -->
    <string name="media_projection_entry_generic_permission_dialog_warning_single_app">When you’re sharing, recording, or casting an app, Android has access to anything shown or played on that app. So be careful with things like passwords, payment details, messages, photos, and audio and video.</string>
    <!-- System sharing media projection permission button to continue. [CHAR LIMIT=60] -->
    <string name="media_projection_entry_generic_permission_dialog_continue">Start</string>

    <!-- Task switcher notification -->
    <!-- Task switcher notification text. [CHAR LIMIT=100] -->
    <string name="media_projection_task_switcher_text">Sharing pauses when you switch apps</string>
    <!-- The action for switching to the foreground task. [CHAR LIMIT=40] -->
    <string name="media_projection_task_switcher_action_switch">Share this app instead</string>
    <!-- The action for switching back to the projected task. [CHAR LIMIT=40] -->
    <string name="media_projection_task_switcher_action_back">Switch back</string>
    <!-- Task switcher notification channel name. [CHAR LIMIT=40] -->
    <string name="media_projection_task_switcher_notification_channel">App switch</string>

    <!-- Title for the dialog that is shown when screen capturing is disabled by enterprise policy. [CHAR LIMIT=100] -->
    <string name="screen_capturing_disabled_by_policy_dialog_title">Blocked by your IT admin</string>

    <!-- Description for the dialog that is shown when screen capturing is disabled by enterprise policy. [CHAR LIMIT=350] -->
    <string name="screen_capturing_disabled_by_policy_dialog_description">Screen capturing is disabled by device policy</string>

    <!-- The text to clear all notifications. [CHAR LIMIT=60] -->
    <string name="clear_all_notifications_text">Clear all</string>

    <!-- The text for the manage notifications link. [CHAR LIMIT=40] -->
    <string name="manage_notifications_text">Manage</string>

    <!-- The text for the notification history link. [CHAR LIMIT=40] -->
    <string name="manage_notifications_history_text">History</string>

    <!-- Section title for notifications that have recently appeared. [CHAR LIMIT=40] -->
    <string name="notification_section_header_incoming">New</string>

    <!-- Section title for notifications that do not vibrate or make noise. [CHAR LIMIT=40] -->
    <string name="notification_section_header_gentle">Silent</string>

    <!-- Section title for notifications that vibrate or make noise. [CHAR LIMIT=40] -->
    <string name="notification_section_header_alerting">Notifications</string>

    <!-- Section title for conversational notifications. [CHAR LIMIT=40] -->
    <string name="notification_section_header_conversations">Conversations</string>

    <!-- Content description for accessibility: Tapping this button will dismiss all gentle notifications [CHAR LIMIT=NONE] -->
    <string name="accessibility_notification_section_header_gentle_clear_all">Clear all silent notifications</string>

    <!-- The text to show in the notifications shade when dnd is suppressing notifications. [CHAR LIMIT=100] -->
    <string name="dnd_suppressing_shade_text">Notifications paused by Do Not Disturb</string>

    <!-- Media projection permission dialog action text. [CHAR LIMIT=60] -->
    <string name="media_projection_action_text">Start now</string>

    <!-- Text which is shown in the notification shade when there are no notifications. [CHAR LIMIT=30] -->
    <string name="empty_shade_text">No notifications</string>

    <!-- Text which is shown in the expanded notification shade when there are currently no notifications visible that the user hasn't already seen. [CHAR LIMIT=30] -->
    <string name="no_unseen_notif_text">No new notifications</string>

    <!-- Text which is shown in the locked notification shade when there are currently no notifications, but if the user were to unlock, notifications would appear. [CHAR LIMIT=40] -->
    <string name="unlock_to_see_notif_text">Unlock to see older notifications</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that parental controls are enabled. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_parental_controls">This device is managed by your parent</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the organization can monitor network traffic on that device. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_monitoring">Your organization owns this device and may monitor network traffic</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the organization can monitor network traffic on that device. The placeholder is the organization's name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management_monitoring"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> owns this device and may monitor network traffic</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's financed device belongs to the Creditor. The placeholder is the Creditor's name. [CHAR LIMIT=100] -->
    <string name="quick_settings_financed_disclosure_named_management">This device is provided by <xliff:g id="organization_name" example="Foo, Inc.">%s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the device is connected to a VPN. The placeholder is the VPN name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_named_vpn">This device belongs to your organization and is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the device is connected to a VPN. The first placeholder is the organization name, and the second placeholder is the VPN name. [CHAR LIMIT=150] -->
    <string name="quick_settings_disclosure_named_management_named_vpn">This device belongs to <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> and is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%2$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management">This device belongs to your organization</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization. The placeholder is the organization's name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management">This device belongs to <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g></string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the device is connected to multiple VPNs. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_management_vpns">This device belongs to your organization and is connected to the internet through VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device belongs to their organization, and the device is connected to multiple VPNs. The placeholder is the organization's name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_management_vpns">This device belongs to <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> and is connected to the internet through VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a managed profile which can be monitored by the profile owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_managed_profile_monitoring">Your organization may monitor network traffic in your work profile</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device has a managed profile which can be monitored by the profile owner [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_managed_profile_monitoring"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> may monitor network traffic in your work profile</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the user's device has a managed profile where network activity is visible to their IT admin [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_managed_profile_network_activity">Work profile network activity is visible to your IT admin</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that a certificate authorithy is installed on this device and the traffic might be monitored [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_monitoring">Network may be monitored</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to multiple VPNs. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_vpns">This device is connected to the internet through VPNs</string>

    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN in the work profile. The placeholder is the VPN name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_managed_profile_named_vpn">Your work apps are connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>


    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN in the personal profile (instead of the work profile). The placeholder is the VPN name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_personal_profile_named_vpn">Your personal apps are connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>


    <!-- Disclosure at the bottom of Quick Settings that indicates that the device is connected to a VPN. The placeholder is the VPN name. [CHAR LIMIT=100] -->
    <string name="quick_settings_disclosure_named_vpn">This device is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g></string>


    <!-- Monitoring dialog title for financed device [CHAR LIMIT=60] -->
    <string name="monitoring_title_financed_device">This device is provided by <xliff:g id="organization_name" example="Foo, Inc.">%s</xliff:g></string>

    <!-- Monitoring dialog title for device owned devices [CHAR LIMIT=35] -->
    <string name="monitoring_title_device_owned">Device management</string>

    <!-- STOPSHIP Monitoring strings still need to be finalized and approved -->
    <!-- Monitoring dialog subtitle for the section describing VPN [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_vpn">VPN</string>

    <!-- Monitoring dialog subtitle for the section describing network logging [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_network_logging">Network logging</string>

    <!-- Monitoring dialog subtitle for the section describing certificate authorities [CHAR LIMIT=35]-->
    <string name="monitoring_subtitle_ca_certificate">CA certificates</string>

    <!-- Monitoring dialog label for button opening a page with more information on the admin's abilities [CHAR LIMIT=30] -->
    <string name="monitoring_button_view_policies">View Policies</string>

    <!-- Monitoring dialog label for button opening a page with more information on parental controls [CHAR LIMIT=30] -->
    <string name="monitoring_button_view_controls">View controls</string>

    <!-- Dialog that a user can access via Quick Settings. The dialog describes what the IT admin can monitor (and the changes they can make) on the user's device. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_named_management">This device belongs to <xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g>.\n\nYour IT admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your IT admin.</string>

    <!-- Dialog that a user can access via Quick Settings. The dialog describes what a Creditor can monitor (and the changes they can make) on the user's financed device. [CHAR LIMIT=NONE]-->
    <string name="monitoring_financed_description_named_management"><xliff:g id="organization_name" example="Foo, Inc.">%1$s</xliff:g> may be able to access data associated with this device, manage apps, and change this device\’s settings.\n\nIf you have questions, contact <xliff:g id="organization_name" example="Foo, Inc.">%2$s</xliff:g>.</string>

    <!-- Dialog that a user can access via Quick Settings. The dialog describes what the IT admin can monitor (and the changes they can make) on the user's device. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management">This device belongs to your organization.\n\nYour IT admin can monitor and manage settings, corporate access, apps, data associated with your device, and your device\'s location information.\n\nFor more information, contact your IT admin.</string>

    <!-- Monitoring dialog: Description of a CA Certificate. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management_ca_certificate">Your organization installed a certificate authority on this device. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of a CA Certificate in the work profile. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_profile_ca_certificate">Your organization installed a certificate authority in your work profile. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of a CA Certificate. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_ca_certificate">A certificate authority is installed on this device. Your secure network traffic may be monitored or modified.</string>

    <!-- Monitoring dialog: Description of Network Logging. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_management_network_logging">Your admin has turned on network logging, which monitors traffic on your device.</string>

    <!-- Monitoring dialog: Description of Network Logging in the work profile. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_profile_network_logging">Your admin has turned on network logging, which monitors traffic in your work profile but not in your personal profile.</string>

    <!-- Monitoring dialog: Description of an active VPN. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_named_vpn">This device is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>. Your network activity, including emails and browsing data, is visible to the VPN provider.</string>

    <!-- Monitoring dialog: Description of an active VPN on a managed device. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_device_named_vpn">This device is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>. Your network activity, including emails and browsing data, is visible to your IT admin.</string>

    <!-- Monitoring dialog: Description of two active VPNs. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_two_named_vpns">This device is connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g> and <xliff:g id="vpn_app" example="Bar VPN App">%2$s</xliff:g>. Your network activity, including emails and browsing data, is visible to your IT admin.</string>

    <!-- Monitoring dialog: Description of an active VPN in the work profile. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_managed_profile_named_vpn">Your work apps are connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>. Your network activity in work apps, including emails and browsing data, is visible to your IT admin and VPN provider.</string>

    <!-- Monitoring dialog: Description of an active VPN in the personal profile (as opposed to the work profile). [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_personal_profile_named_vpn">Your personal apps are connected to the internet through <xliff:g id="vpn_app" example="Foo VPN App">%1$s</xliff:g>. Your network activity, including emails and browsing data, is visible to your VPN provider.</string>

    <!-- Monitoring dialog: Space that separates the VPN body text and the "Open VPN Settings" link that follows it. [CHAR LIMIT=5] -->
    <string name="monitoring_description_vpn_settings_separator">" "</string>

    <!-- Monitoring dialog: Link to open the VPN settings page [CHAR LIMIT=60] -->
    <string name="monitoring_description_vpn_settings">Open VPN settings</string>

    <!-- Dialog that a user can access via Quick Settings. [CHAR LIMIT=NONE]-->
    <string name="monitoring_description_parental_controls">This device is managed by your parent. Your parent can see and manage information such as the apps you use, your location, and your screen time.</string>

    <!-- Name for a generic legacy VPN connection [CHAR LIMIT=20] -->
    <string name="legacy_vpn_name">VPN</string>

    <!-- Indication on the keyguard that appears when a trust agents unlocks the device. [CHAR LIMIT=40] -->
    <string name="keyguard_indication_trust_unlocked">Kept unlocked by TrustAgent</string>

    <!-- Message asking the user to authenticate with primary authentication methods (PIN/pattern/password) or biometrics after the device is locked by adaptive auth. [CHAR LIMIT=70] -->
    <string name="kg_prompt_after_adaptive_auth_lock">Device was locked, too many authentication attempts</string>

    <!-- Indication on the keyguard that appears after the device is locked by adaptive auth. [CHAR LIMIT=60] -->
    <string name="keyguard_indication_after_adaptive_auth_lock">Device locked\nFailed authentication</string>

    <!-- Accessibility string for current zen mode and selected exit condition. A template that simply concatenates existing mode string and the current condition description. [CHAR LIMIT=20] -->
    <string name="zen_mode_and_condition"><xliff:g id="zen_mode" example="Priority interruptions only">%1$s</xliff:g>. <xliff:g id="exit_condition" example="For one hour">%2$s</xliff:g></string>

    <!-- Content description for accessibility (not shown on the screen): volume dialog settings button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_volume_settings">Sound settings</string>

    <!-- Label for the odi caption initial tool tip. [CHAR LIMIT=28] -->
    <string name="volume_odi_captions_tip">Automatically caption media</string>

    <!-- Content description for accessibility: Clear the odi caption tool tip. [CHAR LIMIT=NONE] -->
    <string name="accessibility_volume_close_odi_captions_tip">Close captions tip</string>

    <!-- Content description for accessibility: Captions button. [CHAR LIMIT=NONE] -->
    <string name="volume_odi_captions_content_description">Captions overlay</string>

    <!-- Content description for accessibility: Hint if click will enable. [CHAR LIMIT=NONE] -->
    <string name="volume_odi_captions_hint_enable">enable</string>
    <!-- Content description for accessibility: Hint if click will disable. [CHAR LIMIT=NONE] -->
    <string name="volume_odi_captions_hint_disable">disable</string>

    <!-- Sound and vibration settings dialog title. [CHAR LIMIT=30] -->
    <string name="sound_settings">Sound &amp; vibration</string>
    <!-- Label for button to go to sound settings screen [CHAR_LIMIT=30] -->
    <string name="volume_panel_dialog_settings_button">Settings</string>

    <string name="volume_panel_captioning_title">Live Caption</string>

    <!-- Title for notification after audio lowers -->
    <string name="csd_lowered_title" product="default">Volume lowered to safer level</string>
    <!-- Message shown in notification after system lowers audio -->
    <string name="csd_system_lowered_text" product="default">Headphone volume has been high for longer than recommended</string>
    <!-- Message shown in notification after system lowers audio after 500% of
         sound dosage is reached.
    -->
    <string name="csd_500_system_lowered_text" product="default">Headphone volume has exceeded the safe limit for this week</string>
    <!-- Message for sound dose warning dialog button to keep listening -->
    <string name="csd_button_keep_listening" product="default">Keep listening</string>
    <!-- Message for sound dose warning dialog button to lower volume -->
    <string name="csd_button_lower_volume" product="default">Lower volume</string>

    <!-- content description for audio output chooser [CHAR LIMIT=NONE]-->

    <!-- Screen pinning dialog title. -->
    <string name="screen_pinning_title">App is pinned</string>
    <!-- Screen pinning dialog description. -->
    <string name="screen_pinning_description">This keeps it in view until you unpin. Touch &amp; hold Back and Overview to unpin.</string>
    <string name="screen_pinning_description_recents_invisible">This keeps it in view until you unpin. Touch &amp; hold Back and Home to unpin.</string>
    <string name="screen_pinning_description_gestural">This keeps it in view until you unpin. Swipe up &amp; hold to unpin.</string>
    <!-- Screen pinning dialog description. -->
    <string name="screen_pinning_description_accessible">This keeps it in view until you unpin. Touch &amp; hold Overview to unpin.</string>
    <string name="screen_pinning_description_recents_invisible_accessible">This keeps it in view until you unpin. Touch &amp; hold Home to unpin.</string>
    <!-- Screen pinning security warning: personal data, email, contacts may be exposed while screen is pinned. [CHAR LIMIT=NONE] -->
    <string name="screen_pinning_exposes_personal_data">Personal data may be accessible (such as contacts and email content).</string>
    <!-- Screen pinning security warning: a pinned app can still launch other apps. [CHAR LIMIT=NONE] -->
    <string name="screen_pinning_can_open_other_apps">Pinned app may open other apps.</string>
    <!-- Notify use that they are in Lock-to-app -->
    <string name="screen_pinning_toast">To unpin this app, touch &amp; hold Back and Overview
        buttons</string>
    <string name="screen_pinning_toast_recents_invisible">To unpin this app, touch &amp; hold Back
        and Home buttons</string>
    <!-- Notify (in toast) user how to unpin screen in gesture navigation mode [CHAR LIMIT=NONE] -->
    <string name="screen_pinning_toast_gesture_nav">To unpin this app, swipe up &amp; hold</string>
    <!-- Screen pinning positive response. -->
    <string name="screen_pinning_positive">Got it</string>
    <!-- Screen pinning negative response. -->
    <string name="screen_pinning_negative">No thanks</string>
    <!-- Enter/Exiting screen pinning indication. -->
    <string name="screen_pinning_start">App pinned</string>
    <string name="screen_pinning_exit">App unpinned</string>


    <!-- Hide quick settings tile confirmation title -->

    <!-- Hide quick settings tile confirmation message -->

    <!-- Hide quick settings tile confirmation button -->

    <!-- volume stream names. All nouns. -->
    <string name="stream_voice_call">Call</string> <!-- STREAM_VOICE_CALL -->
    <string name="stream_system">System</string> <!-- STREAM_SYSTEM -->
    <string name="stream_ring">Ring</string> <!-- STREAM_RING -->
    <string name="stream_music">Media</string> <!-- STREAM_MUSIC -->
    <string name="stream_alarm">Alarm</string> <!-- STREAM_ALARM -->
    <string name="stream_notification">Notification</string> <!-- STREAM_NOTIFICATION -->
    <string name="stream_bluetooth_sco">Bluetooth</string> <!-- STREAM_BLUETOOTH_SCO -->
    <string name="stream_system_enforced" translatable="false">System enforced</string> <!-- STREAM_SYSTEM_ENFORCED -->
    <string name="stream_dtmf">Dual multi tone frequency</string> <!-- STREAM_DTMF -->
    <string name="stream_tts" translatable="false">Transmitted Through Speaker</string> <!-- STREAM_TTS -->
    <string name="stream_accessibility">Accessibility</string> <!-- STREAM_ACCESSIBILITY -->

    <string name="volume_ringer_status_normal">Ring</string>
    <string name="volume_ringer_status_vibrate">Vibrate</string>
    <string name="volume_ringer_status_silent">Mute</string>

    <!-- Media device casting volume slider label [CHAR_LIMIT=20] -->
    <string name="media_device_cast">Cast</string>
    <!-- A message shown when the notification volume changing is disabled because of the muted ring stream [CHAR_LIMIT=50]-->
    <string name="stream_notification_unavailable">Unavailable because ring is muted</string>
    <!-- A message shown when the alarm volume changing is disabled because of the don't disturb mode [CHAR_LIMIT=50]-->
    <string name="stream_alarm_unavailable">Unavailable because Do Not Disturb is on</string>
    <!-- A message shown when the media volume changing is disabled because of the don't disturb mode [CHAR_LIMIT=50]-->
    <string name="stream_media_unavailable">Unavailable because Do Not Disturb is on</string>

    <!-- Shown in the header of quick settings to indicate to the user that their phone ringer is on vibrate. [CHAR_LIMIT=NONE] -->
    <!-- Shown in the header of quick settings to indicate to the user that their phone ringer is on silent (muted). [CHAR_LIMIT=NONE] -->

    <string name="volume_stream_content_description_unmute">%1$s. Tap to unmute.</string>
    <string name="volume_stream_content_description_vibrate">%1$s. Tap to set to vibrate. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_mute">%1$s. Tap to mute. Accessibility services may be muted.</string>
    <string name="volume_stream_content_description_vibrate_a11y">%1$s. Tap to set to vibrate.</string>
    <string name="volume_stream_content_description_mute_a11y">%1$s. Tap to mute.</string>

    <!-- Label for button to enabled/disable active noise cancellation [CHAR_LIMIT=30] -->
    <string name="volume_panel_noise_control_title">Noise Control</string>
    <!-- Label for button to enabled/disable spatial audio [CHAR_LIMIT=30] -->
    <string name="volume_panel_spatial_audio_title">Spatial Audio</string>
    <!-- Label for a spatial audio button for the case when it is disabled [CHAR_LIMIT=20] -->
    <string name="volume_panel_spatial_audio_off">Off</string>
    <!-- Label for a spatial audio button for the case when it is enabled without head tracking [CHAR_LIMIT=20] -->
    <string name="volume_panel_spatial_audio_fixed">Fixed</string>
    <!-- Label for a spatial audio button for the case when it is enabled with head tracking [CHAR_LIMIT=20] -->
    <string name="volume_panel_spatial_audio_tracking">Head Tracking</string>

    <string name="volume_ringer_change">Tap to change ringer mode</string>

    <!-- Hint for accessibility. For example: double tap to mute [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_mute">mute</string>
    <!-- Hint for accessibility. For example: double tap to unmute [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_unmute">unmute</string>
    <!-- Hint for accessibility. For example: double tap to vibrate [CHAR_LIMIT=NONE] -->
    <string name="volume_ringer_hint_vibrate">vibrate</string>

    <string name="volume_dialog_title">%s volume controls</string>

    <string name="volume_dialog_ringer_guidance_ring">Calls and notifications will ring (<xliff:g id="volume level" example="56">%1$s</xliff:g>)</string>

    <!-- An audible a11y label for a button, that opens settings when clicked [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_enter_media_output_settings">Enter output settings</string>
    <!-- An audible a11y state description for a button, that expands volume sliders menu [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_expanded_sliders">Volume sliders expanded</string>
    <!-- An audible a11y state description for a button, that collapses volume sliders menu [CHAR LIMIT=NONE] -->
    <string name="volume_panel_collapsed_sliders">Volume sliders collapsed</string>

    <!-- Hint for accessibility. A stream name is a parameter. For example: double tap to mute media [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_hint_mute">Mute %s</string>
    <!-- Hint for accessibility. A stream name is a parameter. For example: double tap to unmute media [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_hint_unmute">Unmute %s</string>

    <!-- Hint for accessibility. This is announced when the stream is muted [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_hint_muted">muted</string>

    <!-- Hint for accessibility. This is announced when ring mode is set to Vibrate. [CHAR_LIMIT=NONE] -->
    <string name="volume_panel_hint_vibrate">vibrate</string>

    <!-- Title with application label for media output settings when there is media playing. [CHAR LIMIT=20] -->
    <string name="media_output_label_title">Playing <xliff:g id="label" example="Music Player">%s</xliff:g> on</string>

    <!-- Title for media output settings without media is playing. [CHAR LIMIT=20] -->
    <string name="media_output_title_without_playing">Audio will play on</string>

    <!-- Title for media output settings when there is an ongoing call in progress. [CHAR LIMIT=20] -->
    <string name="media_output_title_ongoing_call">Calling on</string>

    <!-- Name of special SystemUI debug settings -->
    <string name="system_ui_tuner">System UI Tuner</string>

    <!-- Name of status bar -->
    <string name="status_bar">Status bar</string>

    <!-- Name of System UI demo mode (mode with preset icons for screenshots) -->
    <string name="demo_mode">System UI demo mode</string>

    <!-- Enable demo mode -->
    <string name="enable_demo_mode">Enable demo mode</string>

    <!-- Show demo mode icons -->
    <string name="show_demo_mode">Show demo mode</string>

    <!-- Name of the ethernet status bar icon. -->
    <string name="status_bar_ethernet">Ethernet</string>

    <!-- Name of the alarm status bar icon. -->
    <string name="status_bar_alarm">Alarm</string>

    <!-- Wallet strings -->
    <!-- Wallet empty state, title [CHAR LIMIT=32] -->
    <string name="wallet_title">Wallet</string>
    <!-- Wallet empty state label. [CHAR LIMIT=NONE] -->
    <string name="wallet_empty_state_label">Get set up to make faster, more secure purchases with your phone</string>
    <!-- Label of the button at the bottom prompting user enter wallet app. [CHAR LIMIT=NONE] -->
    <string name="wallet_app_button_label">Show all</string>
    <!-- Label of the button underneath the card carousel prompting user unlock device. [CHAR LIMIT=NONE] -->
    <!-- Secondary label of the quick access wallet tile if no card. [CHAR LIMIT=NONE] -->
    <string name="wallet_secondary_label_no_card">Tap to open</string>
    <!-- Secondary label of the quick access wallet tile if wallet is still updating. [CHAR LIMIT=NONE] -->
    <string name="wallet_secondary_label_updating">Updating</string>
    <!-- Secondary label of the quick access wallet tile if device locked. [CHAR LIMIT=NONE] -->
    <string name="wallet_secondary_label_device_locked">Unlock to use</string>
    <!-- Message shown when an unknown failure occurred when fetching cards. [CHAR LIMIT=NONE] -->
    <string name="wallet_error_generic">There was a problem getting your cards, please try again later</string>
    <!-- Overflow menu item label to open the lockscreen settings in wallet activity. [CHAR LIMIT=NONE] -->
    <string name="wallet_lockscreen_settings_label">Lock screen settings</string>

    <!-- QR Code Scanner label, title [CHAR LIMIT=32] -->
    <string name="qr_code_scanner_title">QR code scanner</string>

    <!-- QR Code Scanner Secondary label when GMS Core is Updating -->
    <string name="qr_code_scanner_updating_secondary_label">Updating</string>

    <!-- Name of the work status bar icon. -->
    <string name="status_bar_work">Work profile</string>

    <!-- Name of the airplane status bar icon. -->
    <string name="status_bar_airplane">Airplane mode</string>

    <!-- Description for adding  a quick settings tile -->

    <!-- Name of a quick settings tile controlled by broadcast -->

    <!-- For preview release. DO NOT TRANSLATE -->

    <!-- Warning text when an alarm might be silenced by Do Not Disturb [CHAR LIMIT=NONE] -->
    <string name="zen_alarm_warning_indef">You won\'t hear your next alarm <xliff:g id="when" example="at 7:00 AM">%1$s</xliff:g> unless you turn this off before then</string>

    <!-- Warning text when an alarm might be silenced by Do Not Disturb due to a time-based condition [CHAR LIMIT=NONE] -->
    <string name="zen_alarm_warning">You won\'t hear your next alarm <xliff:g id="when" example="at 7:00 AM">%1$s</xliff:g></string>

    <!-- Alarm template for near alarms [CHAR LIMIT=25] -->
    <string name="alarm_template">at <xliff:g id="when" example="7:00 AM">%1$s</xliff:g></string>

    <!-- Alarm template for far alarms [CHAR LIMIT=25] -->
    <string name="alarm_template_far">on <xliff:g id="when" example="Fri 7:00 AM">%1$s</xliff:g></string>

    <!-- Accessibility label for hotspot icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_hotspot">Hotspot</string>

    <!-- Accessibility label for no satellite connection [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_satellite_no_connection">Satellite, no connection</string>
    <!-- Accessibility label for poor satellite connection [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_satellite_poor_connection">Satellite, poor connection</string>
    <!-- Accessibility label for good satellite connection [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_satellite_good_connection">Satellite, good connection</string>
    <!-- Accessibility label for available satellite connection [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_satellite_available">Satellite, connection available</string>

    <!-- Text displayed indicating that the user is connected to a satellite signal. -->
    <string name="satellite_connected_carrier_text">Satellite SOS</string>

    <!-- Accessibility label for managed profile icon (not shown on screen) [CHAR LIMIT=NONE] -->
    <string name="accessibility_managed_profile">Work profile</string>

    <!-- Title of warning when entering System UI tuner for first time [CHAR LIMIT=NONE] -->
    <string name="tuner_warning_title">Fun for some but not for all</string>

    <!-- Warning for users entering the System UI tuner for the first time [CHAR LIMIT=NONE]-->
    <string name="tuner_warning">System UI Tuner gives you extra ways to tweak and customize the Android user interface. These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>

    <!-- Warning for users entering the System UI tuner [CHAR LIMIT=NONE]-->
    <string name="tuner_persistent_warning">These experimental features may change, break, or disappear in future releases. Proceed with caution.</string>

    <!-- Generic "got it" acceptance of dialog or cling [CHAR LIMIT=NONE] -->
    <string name="got_it">Got it</string>

    <!-- Toast describing tuner has been enabled [CHAR LIMIT=NONE] -->
    <string name="tuner_toast">Congrats! System UI Tuner has been added to Settings</string>

    <!-- Option to remove the tuner from settings [CHAR LIMIT=NONE] -->
    <string name="remove_from_settings">Remove from Settings</string>

    <!-- Dialog asking if the tuner should really be removed from settings [CHAR LIMIT=NONE]-->
    <string name="remove_from_settings_prompt">Remove System UI Tuner from Settings and stop using all of its features?"</string>

    <string name="qs_customize_remove" translatable="false">Remove</string>

    <!-- Dialog title asking if Bluetooth should be enabled [CHAR LIMIT=NONE] -->
    <string name="enable_bluetooth_title">Turn on Bluetooth?</string>

    <!-- Dialog message explaining why Bluetooth should be enabled when a packaged keyboard is
         conncted to the device [CHAR LIMIT=NONE] -->
    <string name="enable_bluetooth_message">To connect your keyboard with your tablet, you first have to turn on Bluetooth.</string>

    <!-- Bluetooth enablement ok text [CHAR LIMIT=40] -->
    <string name="enable_bluetooth_confirmation_ok">Turn on</string>

    <!-- [CHAR LIMIT=NONE] Notification camera based rotation enabled description -->
    <string name="rotation_lock_camera_rotation_on">On - Face-based</string>

    <!-- Notification Inline controls: button to dismiss the blocking helper [CHAR_LIMIT=20] -->
    <string name="inline_done_button">Done</string>

    <!-- Notification Inline controls: button to dismiss the blocking helper [CHAR_LIMIT=20] -->
    <string name="inline_ok_button">Apply</string>

    <!-- Notification inline controls: button to show block screen [CHAR_LIMIT=35] -->
    <string name="inline_turn_off_notifications">Turn off notifications</string>

    <!-- [CHAR LIMIT=100] Notification Importance title -->
    <string name="notification_silence_title">Silent</string>

    <!-- [CHAR LIMIT=100] Notification Importance title -->
    <string name="notification_alert_title">Default</string>

    <!-- [CHAR LIMIT=100] Notification Importance title -->
    <string name="notification_automatic_title">Automatic</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: low importance level summary -->
    <string name="notification_channel_summary_low">No sound or vibration</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: low importance level summary -->
    <string name="notification_conversation_summary_low">No sound or vibration and appears lower in conversation section</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: normal importance level summary -->
    <string name="notification_channel_summary_default">May ring or vibrate based on device settings</string>

    <!-- [CHAR LIMIT=150] Conversation Notification Importance title: normal conversation level, with bubbling summary -->
    <string name="notification_channel_summary_default_with_bubbles">May ring or vibrate based on device settings. Conversations from <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> bubble by default.</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: automatic importance level summary -->
    <string name="notification_channel_summary_automatic">Have the system determine if this notification should make sound or vibration</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: automatic importance level summary (alerted) -->
    <string name="notification_channel_summary_automatic_alerted">&lt;b>Status:&lt;/b> Promoted to Default</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: automatic importance level summary (silenced) -->
    <string name="notification_channel_summary_automatic_silenced">&lt;b>Status:&lt;/b> Demoted to Silent</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: automatic importance level summary (promoted) -->
    <string name="notification_channel_summary_automatic_promoted">&lt;b>Status:&lt;/b> Ranked Higher</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: automatic importance level summary (demoted) -->
    <string name="notification_channel_summary_automatic_demoted">&lt;b>Status:&lt;/b> Ranked Lower</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: important conversation level summary -->
    <string name="notification_channel_summary_priority_baseline">Shows at the top of conversation notifications and as a profile picture on lock screen</string>
    <string name="notification_channel_summary_priority_bubble">Shows at the top of conversation notifications and as a profile picture on lock screen, appears as a bubble</string>
    <string name="notification_channel_summary_priority_dnd">Shows at the top of conversation notifications and as a profile picture on lock screen, interrupts Do Not Disturb</string>
    <string name="notification_channel_summary_priority_all">Shows at the top of conversation notifications and as a profile picture on lock screen, appears as a bubble, interrupts Do Not Disturb</string>

    <!-- [CHAR LIMIT=150] Notification Importance title: important conversation level -->
    <string name="notification_priority_title">Priority</string>

    <!-- Text shown in notification guts for conversation notifications that don't implement the full feature -->
    <string name="no_shortcut"><xliff:g id="app_name" example="YouTube">%1$s</xliff:g> doesn\u2019t support conversation features</string>

    <!-- Notification: Control panel: Label that displays when the app's notifications cannot be blocked. -->
    <string name="notification_unblockable_desc">These notifications can\'t be modified.</string>

    <!-- Notification: Control panel: Label that displays when a notification cannot be blocked because it's attached to a phone/voip call. -->
    <string name="notification_unblockable_call_desc">Call notifications can\'t be modified.</string>

    <!-- Notification: Control panel: label that displays when viewing settings for a group of notifications posted to multiple channels. -->
    <string name="notification_multichannel_desc">This group of notifications cannot be configured here</string>

    <!-- Notification: Control panel: Label for the app that posted this notification, if it's not the package that the notification was posted for -->
    <string name="notification_delegate_header">Proxied notification</string>

    <!-- [CHAR LIMIT=40 Notification: Label for the inline channel blocking view -->
    <string name="notification_channel_dialog_title">All <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> notifications</string>

    <!-- [CHAR LIMIT=20 Notification: "See more" button -->
    <string name="see_more_title">See more</string>

    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->
    <!-- Notification Inline controls: describes what the app is doing in the background [CHAR_LIMIT=NONE] -->

    <!-- Notification Inline controls: describes how the notification was adjusted [CHAR_LIMIT=NONE] -->
    <string name="feedback_alerted">This notification was automatically &lt;b>promoted to Default&lt;/b> by the system.</string>
    <!-- Notification Inline controls: describes how the notification was adjusted [CHAR_LIMIT=NONE] -->
    <string name="feedback_silenced">This notification was automatically &lt;b>demoted to Silent&lt;/b> by the system.</string>
    <!-- Notification Inline controls: describes how the notification was adjusted [CHAR_LIMIT=NONE] -->
    <string name="feedback_promoted">This notification was automatically &lt;b>ranked higher&lt;/b> in your shade.</string>
    <!-- Notification Inline controls: describes how the notification was adjusted [CHAR_LIMIT=NONE] -->
    <string name="feedback_demoted">This notification was automatically &lt;b>ranked lower&lt;/b> in your shade.</string>
    <!-- Notification Inline controls: prompts the user for feedback [CHAR_LIMIT=NONE] -->
    <string name="feedback_prompt">Let the developer know your feedback. Was this correct?</string>
    <!-- Notification Inline controls: responds to user provided feedback [CHAR_LIMIT=NONE] -->

    <!-- Notification: Control panel: Accessibility description for expanded inline controls view, used
        to control settings about notifications related to the current notification.  -->
    <string name="notification_channel_controls_opened_accessibility">Notification controls for <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> opened</string>
    <!-- Notification: Control panel: Accessibility description for announcing the closing of the
        inline controls view.  -->
    <string name="notification_channel_controls_closed_accessibility">Notification controls for <xliff:g id="app_name" example="YouTube">%1$s</xliff:g> closed</string>
    <!-- Notification: Control panel: Accessibility description for switch that is used to enable
        or disable notifications from this channel -->
    <!-- Notification: Control panel: Label for button that launches notification settings. Used
        when this app has only defined a single channel for notifications. -->
    <string name="notification_more_settings">More settings</string>
    <!-- Notification: Control panel: Label for a link that launches notification settings in the
        app that sent the notification. -->
    <string name="notification_app_settings">Customize</string>

    <!-- [CHAR LIMIT=100] Show notification as bubble -->
    <string name="notification_conversation_bubble">Show bubble</string>

    <!-- [CHAR LIMIT=100] Turn off bubbles for notification -->
    <string name="notification_conversation_unbubble">Remove bubbles</string>

    <!-- Notification: Menu row: Content description for menu items. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_accessibility"><xliff:g id="app_name" example="YouTube">%1$s</xliff:g> <xliff:g id="menu_description" example="notification controls">%2$s</xliff:g></string>

    <!-- Notification: Menu row: Content description for the gear menu item. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_gear_description">notification controls</string>

    <!-- Notification: Menu row: Content description for the snooze icon. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_snooze_description">notification snooze options</string>

    <!-- Notification: Menu row: Label for the snooze action shown in local context menu. [CHAR LIMIT=NONE] -->
    <string name="notification_menu_snooze_action">Remind me</string>

    <!-- Notification: Snooze panel: Snooze undo button label. [CHAR LIMIT=50]-->
    <string name="snooze_undo">Undo</string>

    <!-- Notification: Snooze panel: Snooze undo content description for a11y. [CHAR LIMIT=NONE]-->
    <string name="snooze_undo_content_description">Undo notification snooze</string>

    <!-- Notification: Snooze panel: message indicating how long the notification was snoozed for. [CHAR LIMIT=100]-->
    <string name="snoozed_for_time">Snoozed for <xliff:g id="time_amount" example="15 minutes">%1$s</xliff:g></string>

    <!-- Notification:Snooze panel: Snooze options for hours [CHAR LIMIT=NONE]-->
    <string name="snoozeHourOptions">{count, plural,
        =1 {# hour}
        =2 {# hours}
        few {# hours}
        other {# hours}
    }</string>

   <!--  Notification: Snooze panel: Snooze options for minutes [CHAR LIMIT=NONE]-->
   <string name="snoozeMinuteOptions">{count, plural,
        =1 {# minute}
        few {# minutes}
        other {# minutes}
    }</string>

    <!-- Summary of battery saver not available [CHAR LIMIT=NONE] -->

    <!-- Title of switch for battery saver [CHAR LIMIT=NONE] -->
    <string name="battery_detail_switch_title">Battery Saver</string>

    <!-- Summary of switch for battery saver [CHAR LIMIT=NONE] -->

    <!-- Name used for certain Keyboard keys on gamepads, e.g. "Button L1". -->
    <string name="keyboard_key_button_template">Button <xliff:g id="name">%1$s</xliff:g></string>
    <!-- Name used to refer to the "Home" key on the keyboard. -->
    <string name="keyboard_key_home">Home</string>
    <!-- Name used to refer to the "Back" key on the keyboard. -->
    <string name="keyboard_key_back">Back</string>
    <!-- Name used to refer to the "Up" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_up" translatable="false">Up arrow</string>
    <!-- Name used to refer to the "Down" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_down" translatable="false">Down arrow</string>
    <!-- Name used to refer to the "Left" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_left" translatable="false">Left arrow</string>
    <!-- Name used to refer to the "Right" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_right" translatable="false">Right arrow</string>
    <!-- Name used to refer to the "Center" arrow key on the keyboard. -->
    <string name="keyboard_key_dpad_center" translatable="false">Center</string>
    <!-- Name used to refer to the "Tab" key on the keyboard. -->
    <string name="keyboard_key_tab">Tab</string>
    <!-- Name used to refer to the "Space" key on the keyboard. -->
    <string name="keyboard_key_space">Space</string>
    <!-- Name used to refer to the "Enter" key on the keyboard. -->
    <string name="keyboard_key_enter">Enter</string>
    <!-- Name used to refer to the "Backspace" key on the keyboard. -->
    <string name="keyboard_key_backspace">Backspace</string>
    <!-- Name used to refer to the "Play/Pause" media key on the keyboard. -->
    <string name="keyboard_key_media_play_pause">Play/Pause</string>
    <!-- Name used to refer to the "Stop" media key on the keyboard. -->
    <string name="keyboard_key_media_stop">Stop</string>
    <!-- Name used to refer to the "Next" media key on the keyboard. -->
    <string name="keyboard_key_media_next">Next</string>
    <!-- Name used to refer to the "Previous" media key on the keyboard. -->
    <string name="keyboard_key_media_previous">Previous</string>
    <!-- Name used to refer to the "Rewind" media key on the keyboard. -->
    <string name="keyboard_key_media_rewind">Rewind</string>
    <!-- Name used to refer to the "Fast Forward" media key on the keyboard. -->
    <string name="keyboard_key_media_fast_forward">Fast Forward</string>
    <!-- Name used to refer to the "Page Up" key on the keyboard. -->
    <string name="keyboard_key_page_up">Page Up</string>
    <!-- Name used to refer to the "Page Down" key on the keyboard. -->
    <string name="keyboard_key_page_down">Page Down</string>
    <!-- Name used to refer to the "Delete" key on the keyboard. -->
    <string name="keyboard_key_forward_del">Delete</string>
    <!-- Name used to refer to the "Home" move key on the keyboard. -->
    <string name="keyboard_key_move_home">Home</string>
    <!-- Name used to refer to the "End" move key on the keyboard. -->
    <string name="keyboard_key_move_end">End</string>
    <!-- Name used to refer to the "Insert" key on the keyboard. -->
    <string name="keyboard_key_insert">Insert</string>
    <!-- Name used to refer to the "Num Lock" key on the keyboard. -->
    <string name="keyboard_key_num_lock">Num Lock</string>
    <!-- Name used to refer to keys on the numeric pad of the keyboard, e.g. "Numpad 9". -->
    <string name="keyboard_key_numpad_template">Numpad <xliff:g id="name">%1$s</xliff:g></string>
    <!-- Content description for the delete button on an image attachment when using inline reply via notification [CHAR LIMIT=NONE] -->
    <string name="notif_inline_reply_remove_attachment_description">Remove attachment</string>
    <!-- User visible title for the system-wide keyboard shortcuts list. -->
    <string name="keyboard_shortcut_group_system">System</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the home screen. -->
    <string name="keyboard_shortcut_group_system_home">Home</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the recents screen. -->
    <string name="keyboard_shortcut_group_system_recents">Recents</string>
    <!-- User visible title for the keyboard shortcut that triggers the back action. -->
    <string name="keyboard_shortcut_group_system_back">Back</string>
    <!-- User visible title for the keyboard shortcut that triggers the notification shade. -->
    <string name="keyboard_shortcut_group_system_notifications">Notifications</string>
    <!-- User visible title for the keyboard shortcut that triggers the keyboard shortcuts helper. -->
    <string name="keyboard_shortcut_group_system_shortcuts_helper">Keyboard Shortcuts</string>
    <!-- User visible title for the keyboard shortcut that switches to the next hardware keyboard layout. -->
    <string name="keyboard_shortcut_group_system_switch_input">Switch keyboard layout</string>
    <!-- User visible string that joins different shortcuts in a list, e.g. shortcut1 "or" shortcut2 "or" ... -->
    <string  name="keyboard_shortcut_join">or</string>

    <!-- Content description for the clear search button in shortcut search list. [CHAR LIMIT=NONE] -->
    <string name="keyboard_shortcut_clear_text">Clear search query</string>
    <!-- The title for keyboard shortcut search list [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_list_title">Keyboard Shortcuts</string>
    <!-- The hint for keyboard shortcut search list [CHAR LIMIT=50] -->
    <string name="keyboard_shortcut_search_list_hint">Search shortcuts</string>
    <!-- The description for no shortcuts results [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_list_no_result">No shortcuts found</string>
    <!-- The title of system category in shortcut search list. [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_category_system">System</string>
    <!-- The title of input category in shortcut search list. [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_category_input">Input</string>
    <!-- The title of open apps category in shortcut search list. [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_category_open_apps">Open apps</string>
    <!-- The title of current app category in shortcut search list. [CHAR LIMIT=25] -->
    <string name="keyboard_shortcut_search_category_current_app">Current app</string>

    <!-- A11y message when showing keyboard shortcut search results. [CHAR LIMT=NONE] -->
    <string name="keyboard_shortcut_a11y_show_search_results">Showing search results</string>
    <!-- A11y message when filtering to "system" keyboard shortcuts.  [CHAR LIMT=NONE] -->
    <string name="keyboard_shortcut_a11y_filter_system">Showing system shortcuts</string>
    <!-- A11y message when filtering to "input" keyboard shortcuts.  [CHAR LIMT=NONE] -->
    <string name="keyboard_shortcut_a11y_filter_input">Showing input shortcuts</string>
    <!-- A11y message when filtering to "app opening" keyboard shortcuts.  [CHAR LIMT=NONE] -->
    <string name="keyboard_shortcut_a11y_filter_open_apps">Showing shortcuts that open apps</string>
    <!-- A11y message when filtering to "current app" keyboard shortcuts.  [CHAR LIMT=NONE] -->
    <string name="keyboard_shortcut_a11y_filter_current_app">Showing shortcuts for the current app</string>

    <!-- User visible title for the keyboard shortcut that triggers the notification shade. [CHAR LIMIT=70] -->
    <string name="group_system_access_notification_shade">View notifications</string>
    <!-- User visible title for the keyboard shortcut that takes a full screenshot. [CHAR LIMIT=70] -->
    <string name="group_system_full_screenshot">Take screenshot</string>
    <!-- User visible title for the keyboard shortcut that access list of system / apps shortcuts. [CHAR LIMIT=70] -->
    <string name="group_system_access_system_app_shortcuts">Show shortcuts</string>
    <!-- User visible title for the keyboard shortcut that goes back to previous state. [CHAR LIMIT=70] -->
    <string name="group_system_go_back">Go back</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the home screen. [CHAR LIMIT=70] -->
    <string name="group_system_access_home_screen">Go to home screen</string>
    <!-- User visible title for the keyboard shortcut that triggers overview of open apps. [CHAR LIMIT=70] -->
    <string name="group_system_overview_open_apps">View recent apps</string>
    <!-- User visible title for the keyboard shortcut that cycles through recent apps (forward). [CHAR LIMIT=70] -->
    <string name="group_system_cycle_forward">Cycle forward through recent apps</string>
    <!-- User visible title for the keyboard shortcut that cycles through recent apps (back). [CHAR LIMIT=70] -->
    <string name="group_system_cycle_back">Cycle backward through recent apps</string>
    <!-- User visible title for the keyboard shortcut that accesses list of all apps and search. [CHAR LIMIT=70] -->
    <string name="group_system_access_all_apps_search">Open apps list</string>
    <!-- User visible title for the keyboard shortcut that accesses [system] settings. [CHAR LIMIT=70] -->
    <string name="group_system_access_system_settings">Open settings</string>
    <!-- User visible title for the keyboard shortcut that accesses Assistant app. [CHAR LIMIT=70] -->
    <string name="group_system_access_google_assistant">Open assistant</string>
    <!-- User visible title for the keyboard shortcut that locks screen. [CHAR LIMIT=70] -->
    <string name="group_system_lock_screen">Lock screen</string>
    <!-- User visible title for the keyboard shortcut that pulls up Notes app for quick memo. [CHAR LIMIT=70] -->
    <string name="group_system_quick_memo">Take a note</string>

    <!-- User visible title for the multitasking keyboard shortcuts list. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_system_multitasking">Multitasking</string>
    <!-- User visible title for the keyboard shortcut that enters split screen with current app on the right [CHAR LIMIT=70] -->
    <string name="system_multitasking_rhs">Use split screen with current app on the right</string>
    <!-- User visible title for the keyboard shortcut that enters split screen with current app on the left [CHAR LIMIT=70] -->
    <string name="system_multitasking_lhs">Use split screen with current app on the left</string>
    <!-- User visible title for the keyboard shortcut that switches from split screen to full screen [CHAR LIMIT=70] -->
    <string name="system_multitasking_full_screen">Switch from split screen to full screen</string>
    <!-- User visible title for the keyboard shortcut that switches to app on right or below while using split screen [CHAR LIMIT=70] -->
    <string name="system_multitasking_splitscreen_focus_rhs">Switch to app on right or below while using split screen</string>
    <!-- User visible title for the keyboard shortcut that switches to app on left or above while using split screen [CHAR LIMIT=70] -->
    <string name="system_multitasking_splitscreen_focus_lhs">Switch to app on left or above while using split screen</string>
    <!-- User visible title for the keyboard shortcut that replaces an app from one to another during split screen [CHAR LIMIT=70] -->
    <string name="system_multitasking_replace">During split screen: replace an app from one to another</string>

    <!-- User visible title for the input keyboard shortcuts list. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_input">Input</string>
    <!-- User visible title for the keyboard shortcut that switches input language (next language). [CHAR LIMIT=70] -->
    <string name="input_switch_input_language_next">Switch to next language</string>
    <!-- User visible title for the keyboard shortcut that switches input language (previous language). [CHAR LIMIT=70] -->
    <string name="input_switch_input_language_previous">Switch to previous language</string>
    <!-- User visible title for the keyboard shortcut that accesses emoji. [CHAR LIMIT=70] -->
    <string name="input_access_emoji">Access emoji</string>
    <!-- User visible title for the keyboard shortcut that accesses voice typing. [CHAR LIMIT=70] -->
    <string name="input_access_voice_typing">Access voice typing</string>

    <!-- User visible title for the system-wide applications keyboard shortcuts list. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications">Applications</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the assistant app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_assist">Assistant</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the browser app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_browser">Browser</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the contacts app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_contacts">Contacts</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the email app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_email">Email</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the SMS messaging app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_sms">SMS</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the music app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_music">Music</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the calendar app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_calendar">Calendar</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the calculator app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_calculator">Calculator</string>
    <!-- User visible title for the keyboard shortcut that takes the user to the maps app. [CHAR LIMIT=70] -->
    <string name="keyboard_shortcut_group_applications_maps">Maps</string>

    <!-- SysUI Tuner: Label for screen about do not disturb settings [CHAR LIMIT=60] -->
    <string name="volume_and_do_not_disturb">Do Not Disturb</string>

    <!-- SysUI Tuner: Switch to control whether volume buttons enter/exit do
         not disturb [CHAR LIMIT=60] -->
    <string name="volume_dnd_silent">Volume buttons shortcut</string>

    <!-- Name of the battery icon in status bar [CHAR LIMIT=30] -->
    <string name="battery">Battery</string>

    <!-- Name of the headset in status bar [CHAR LIMIT=30] -->
    <string name="headset">Headset</string>

    <!-- Accessibility description for long click on a quick settings tile - this is used in the
         context of the sentence "double tap and hold to _Open settings_" [CHAR LIMIT=NONE] -->
    <string name="accessibility_long_click_tile">Open settings</string>

    <!-- Accessibility description of headphones icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_headphones">Headphones connected</string>

    <!-- Accessibility description of headset icon [CHAR LIMIT=NONE] -->
    <string name="accessibility_status_bar_headset">Headset connected</string>

    <!-- Label for quick settings tile for data saver [CHAR LIMIT=30] -->
    <string name="data_saver">Data Saver</string>

    <!-- Accessibility description for data saver being on [CHAR LIMIT=NONE] -->
    <string name="accessibility_data_saver_on">Data Saver is on</string>

    <!-- Accessibility description for data saver being off [CHAR LIMIT=NONE] -->

    <!-- Label for feature switch [CHAR LIMIT=30] -->
    <string name="switch_bar_on">On</string>

    <!-- Label for feature switch [CHAR LIMIT=30] -->
    <string name="switch_bar_off">Off</string>

    <!-- The tile in quick settings is unavailable. [CHAR LIMIT=32] -->
    <string name="tile_unavailable">Unavailable</string>

    <!-- Accessibility text for the click action on a tile that is disabled by policy. This will
         be used following "Double-tap to..." [CHAR LIMIT=NONE] -->
    <string name="accessibility_tile_disabled_by_policy_action_description">learn more</string>

    <!-- SysUI Tuner: Button that leads to the navigation bar customization screen [CHAR LIMIT=60] -->
    <string name="nav_bar">Navigation bar</string>

    <!-- Label for navigation edge panel for gestures [CHAR LIMIT=60] -->
    <string name="nav_bar_edge_panel" translatable="false">Navigation bar Edge Panel</string>

    <!-- SysUI Tuner: Button that controls layout of navigation bar [CHAR LIMIT=60] -->
    <string name="nav_bar_layout">Layout</string>

    <!-- SysUI Tuner: Setting for button type in nav bar [CHAR LIMIT=60] -->
    <string name="left_nav_bar_button_type">Extra left button type</string>

    <!-- SysUI Tuner: Setting for button type in nav bar [CHAR LIMIT=60] -->
    <string name="right_nav_bar_button_type">Extra right button type</string>

    <!-- SysUI Tuner: Labels for different types of navigation bar buttons [CHAR LIMIT=60] -->
    <string-array name="nav_bar_buttons">
        <item>Clipboard</item>
        <item>Keycode</item>
        <item>Rotate confirm, keyboard switcher</item>
        <item>None</item>
    </string-array>
    <string-array name="nav_bar_button_values" translatable="false">
        <item>clipboard</item>
        <item>key</item>
        <item>menu_ime</item>
        <item>space</item>
    </string-array>

    <!-- SysUI Tuner: Labels for different types of navigation bar layouts [CHAR LIMIT=60] -->
    <string-array name="nav_bar_layouts">
        <item>Normal</item>
        <item>Compact</item>
        <item>Left-leaning</item>
        <item>Right-leaning</item>
    </string-array>

    <string-array name="nav_bar_layouts_values" translatable="false">
        <item>default</item>
        <item>left;back,home,recent;right</item>
        <item>left,back,home,recent,right;space;space</item>
        <item>space;space;left,back,home,recent,right</item>
    </string-array>

    <!-- SysUI Tuner: Save the current settings [CHAR LIMIT=30] -->
    <string name="save">Save</string>
    <!-- SysUI Tuner: Reset to default settings [CHAR LIMIT=30] -->
    <string name="reset">Reset</string>

    <!-- SysUI Tuner: Nav bar button that holds the clipboard [CHAR LIMIT=30] -->
    <string name="clipboard">Clipboard</string>

    <!-- SysUI Tuner: Accessibility description for custom nav key [CHAR LIMIT=NONE] -->
    <string name="accessibility_key">Custom navigation button</string>

    <!-- SysUI Tuner: Nav bar button that emulates a keycode [CHAR LIMIT=30] -->
    <string name="left_keycode">Left keycode</string>

    <!-- SysUI Tuner: Nav bar button that emulates a keycode [CHAR LIMIT=30] -->
    <string name="right_keycode">Right keycode</string>

    <!-- SysUI Tuner: Settings to change nav bar icon [CHAR LIMIT=30] -->
    <string name="left_icon">Left icon</string>

    <!-- SysUI Tuner: Settings to change nav bar icon [CHAR LIMIT=30] -->
    <string name="right_icon">Right icon</string>

    <!-- Label for area where tiles can be dragged out of [CHAR LIMIT=60] -->
    <string name="drag_to_add_tiles">Hold and drag to add tiles</string>

    <!-- Label for header of customize QS [CHAR LIMIT=60] -->
    <string name="drag_to_rearrange_tiles">Hold and drag to rearrange tiles</string>

    <!-- Label for area where tiles can be dragged in to [CHAR LIMIT=60] -->
    <string name="drag_to_remove_tiles">Drag here to remove</string>

    <!-- Label to indicate to users that additional tiles cannot be removed. [CHAR LIMIT=60] -->
    <string name="drag_to_remove_disabled">You need at least <xliff:g id="min_num_tiles" example="6">%1$d</xliff:g> tiles</string>

    <!-- Button to edit the tile ordering of quick settings [CHAR LIMIT=60] -->
    <string name="qs_edit">Edit</string>

    <!-- SysUI Tuner: Options for how clock is displayed [CHAR LIMIT=NONE] -->
    <string name="tuner_time">Time</string>

    <!-- SysUI Tuner: Options for how clock is displayed [CHAR LIMIT=NONE] -->
    <string-array name="clock_options">
        <item>Show hours, minutes, and seconds</item>
        <item>Show hours and minutes (default)</item>
        <item>Don\'t show this icon</item>
    </string-array>

    <!-- SysUI Tuner: Options for how battery is displayed [CHAR LIMIT=NONE] -->
    <string-array name="battery_options">
        <item>Always show percentage</item>
        <item>Show percentage when charging (default)</item>
        <item>Don\'t show this icon</item>
    </string-array>

    <!-- SysUI Tuner: Switch for showing low-priority notification icons in status bar [CHAR LIMIT=NONE] -->
    <string name="tuner_low_priority">Show low-priority notification icons</string>

    <!-- SysUI Tuner: Other section -->
    <string name="other">Other</string>

    <!-- Accessibility description of action to remove QS tile on click. It will read as "Double-tap to remove tile" in screen readers [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_remove_tile_action">remove tile</string>

    <!-- Accessibility action of action to add QS tile to end. It will read as "Double-tap to add tile to end" in screen readers [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_add_action">add tile to end</string>

    <!-- Accessibility action for context menu to move QS tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_start_move">Move tile</string>

    <!-- Accessibility action for context menu to add QS tile [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_start_add">Add tile</string>

    <!-- Accessibility description when QS tile is to be moved, indicating the destination position [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_move_to_position">Move to <xliff:g id="position" example="5">%1$d</xliff:g></string>

    <!-- Accessibility description when QS tile is to be added, indicating the destination position [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_add_to_position">Add to position <xliff:g id="position" example="5">%1$d</xliff:g></string>

    <!-- Accessibility description when QS tile would be added or moved, but the current position is not valid for adding or moving to [CHAR LIMIT=NONE] -->
    <string name="accessibilit_qs_edit_tile_add_move_invalid_position">Position invalid.</string>

    <!-- Accessibility description indicating the currently selected tile's position. Only used for tiles that are currently in use [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_position">Position <xliff:g id="position" example="5">%1$d</xliff:g></string>

    <!-- Accessibility announcement after a tile has been added [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_added">Tile added</string>

    <!-- Accessibility announcement after a tile has been added [CHAR LIMIT=NONE] -->
    <string name="accessibility_qs_edit_tile_removed">Tile removed</string>

    <!-- Accessibility label for window when QS editing is happening [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_quick_settings_edit">Quick settings editor.</string>

    <!-- Accessibility label for the notification icons in the collapsed status bar. Not shown on screen [CHAR LIMIT=NONE] -->
    <string name="accessibility_desc_notification_icon"><xliff:g name="app_name" example="Gmail">%1$s</xliff:g> notification: <xliff:g name="notification_text" example="5 new messages">%2$s</xliff:g></string>

    <!-- Label for button that reports a touch that was wrongly rejected by the lockscreen. For debugging only. [CHAR LIMIT=NONE] -->
    <string name="report_rejected_touch" translatable="false">Report rejected touch</string>

    <!-- accessibility label for button to open settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_settings">Open settings.</string>

    <!-- accessibility label for button to expand quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_expand">Open quick settings.</string>

    <!-- accessibility label for button to collapse quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_collapse">Close quick settings.</string>

    <!-- accessibility label for alarm icon [CHAR LIMIT=NONE] -->

    <!-- accessibility label for button to select user [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_user">Signed in as <xliff:g name="user" example="John">%s</xliff:g></string>

    <!-- Accessibility description (not shown on the screen) of action to open user switcher when the multi-user icon is clicked. It will read as "Double-tap to choose user" in screen readers [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_choose_user_action">choose user</string>

    <!-- Content description for no internet connection [CHAR LIMIT=NONE] -->
    <string name="data_connection_no_internet">No internet</string>

    <!-- accessibility label for quick settings items that open a details page [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_open_settings">Open <xliff:g name="page" example="Bluetooth">%s</xliff:g> settings.</string>

    <!-- accessibility label for button to edit quick settings [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_edit">Edit order of settings.</string>

    <!-- accessibility label for button to open power menu [CHAR LIMIT=NONE] -->
    <string name="accessibility_quick_settings_power_menu">Power menu</string>

    <!-- accessibility label for paging indicator in quick settings [CHAR LIMITi=NONE] -->
    <string name="accessibility_quick_settings_page">Page <xliff:g name="current_page" example="1">%1$d</xliff:g> of <xliff:g name="num_pages" example="2">%2$d</xliff:g></string>

    <!-- Plugin control section of the tuner. Non-translatable since it should
         not appear on production builds ever. -->
    <string name="plugins" translatable="false">Plugins</string>

    <!-- Ambient display section of the tuner. Non-translatable since it should
        not appear on production builds ever. -->
    <string name="tuner_doze" translatable="false">Ambient Display</string>

    <!-- Ambient display always-on of the tuner. Non-translatable since it should
        not appear on production builds ever. -->
    <string name="tuner_doze_always_on" translatable="false">Always on</string>

    <!-- SysUI Tuner: Section to customize lockscreen shortcuts [CHAR LIMIT=60] -->
    <string name="tuner_lock_screen">Lock screen</string>

    <!-- Tuner string -->
    <!-- Tuner string -->
    <!-- Tuner string -->

    <!-- Message shown during shutdown when Find My Device with Dead Battery Finder is active  [CHAR LIMIT=300] -->
    <string name="finder_active">You can locate this phone with Find My Device even when powered off</string>
    <!-- Shutdown Progress Dialog. This is shown if the user chooses to power off the phone. [CHAR LIMIT=60] -->
    <string name="shutdown_progress">Shutting down\u2026</string>

    <!-- Text help link for care instructions for overheating devices [CHAR LIMIT=40] -->
    <string name="thermal_shutdown_dialog_help_text">See care steps</string>
    <!-- URL for care instructions for overheating devices -->
    <string name="thermal_shutdown_dialog_help_url" translatable="false"></string>

    <!-- Text help link for care instructions for overheating devices [CHAR LIMIT=40] -->
    <string name="high_temp_dialog_help_text">See care steps</string>
    <!-- URL for care instructions for overheating devices -->
    <string name="high_temp_dialog_help_url" translatable="false"></string>

    <!-- Title for alarm dialog alerting user the usb charger or accessorry has reached a certain temperature that should unplug the device immediately. [CHAR LIMIT=30] -->
    <string name="high_temp_alarm_title">Unplug your device</string>
    <!-- Text body for dialog alerting user the usb charger or accessorry has reached a certain temperature that should unplug the device immediately. [CHAR LIMIT=300] -->
    <string name="high_temp_alarm_notify_message">Your device is getting warm near the charging port. If it\u2019s connected to a charger or USB accessory, unplug it, and take care as the cable may also be warm.</string>
    <!-- Text for See care steps button [CHAR LIMIT=300] -->
    <string name="high_temp_alarm_help_care_steps">See care steps</string>
    <!-- Text link directs to usb overheat help page. -->
    <string name="high_temp_alarm_help_url" translatable="false">help_uri_usb_warm</string>

    <!-- SysUI Tuner: Button to select lock screen shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_shortcut_left">Left shortcut</string>

    <!-- SysUI Tuner: Button to select lock screen shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_shortcut_right">Right shortcut</string>

    <!-- SysUI Tuner: Switch to control if device gets unlocked by left shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_unlock_left">Left shortcut also unlocks</string>

    <!-- SysUI Tuner: Switch to control if device gets unlocked by right shortcut [CHAR LIMIT=60] -->
    <string name="lockscreen_unlock_right">Right shortcut also unlocks</string>

    <!-- SysUI Tuner: Summary of no shortcut being selected [CHAR LIMIT=60] -->
    <string name="lockscreen_none">None</string>

    <!-- ClockId to use when none is set by user -->
    <string name="lockscreen_clock_id_fallback" translatable="false">DEFAULT</string>

    <!-- SysUI Tuner: Format string for describing launching an app [CHAR LIMIT=60] -->
    <string name="tuner_launch_app">Launch <xliff:g id="app" example="Settings">%1$s</xliff:g></string>

    <!-- SysUI Tuner: Label for section of other apps that can be launched [CHAR LIMIT=60] -->
    <string name="tuner_other_apps">Other apps</string>

    <!-- SysUI Tuner: Label for icon shaped like a circle [CHAR LIMIT=60] -->
    <string name="tuner_circle">Circle</string>

    <!-- SysUI Tuner: Label for icon shaped like a plus [CHAR LIMIT=60] -->
    <string name="tuner_plus">Plus</string>

    <!-- SysUI Tuner: Label for icon shaped like a minus [CHAR LIMIT=60] -->
    <string name="tuner_minus">Minus</string>

    <!-- SysUI Tuner: Label for icon shaped like a left [CHAR LIMIT=60] -->
    <string name="tuner_left">Left</string>

    <!-- SysUI Tuner: Label for icon shaped like a right [CHAR LIMIT=60] -->
    <string name="tuner_right">Right</string>

    <!-- SysUI Tuner: Label for icon shaped like a menu [CHAR LIMIT=60] -->
    <string name="tuner_menu">Menu</string>

    <!-- SysUI Tuner: App subheading for shortcut selection [CHAR LIMIT=60] -->
    <string name="tuner_app"><xliff:g id="app">%1$s</xliff:g> app</string>

    <!-- Title for the notification channel containing important alerts. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_alerts">Alerts</string>
    <!-- Title for the notification channel for battery warnings (i.e. < 15%). [CHAR LIMIT=NONE] -->
    <string name="notification_channel_battery">Battery</string>
    <!-- Title for the notification channel dedicated to screenshot progress. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_screenshot">Screenshots</string>
    <!-- Title for the notification channel for instant app notices. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_instant">Instant Apps</string>
    <!-- Title for the notification channel for setup notices. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_setup">Setup</string>
    <!-- Title for the notification channel for problems with storage (i.e. low disk). [CHAR LIMIT=NONE] -->
    <string name="notification_channel_storage">Storage</string>
    <!-- Title for the notification channel for hints and suggestions. [CHAR LIMIT=NONE] -->
    <string name="notification_channel_hints">Hints</string>
    <!-- Title for the notification channel for accessibility related (i.e. accessibility floating menu). [CHAR LIMIT=NONE] -->
    <string name="notification_channel_accessibility">Accessibility</string>

    <!-- App label of the instant apps notification [CHAR LIMIT=60] -->
    <string name="instant_apps">Instant Apps</string>

    <!-- Title of notification indicating that an instant app is running. [CHAR LIMIT=60] -->
    <string name="instant_apps_title"><xliff:g id="app" example="Gmail">%1$s</xliff:g> running</string>

    <!-- Message of the instant apps notification indicating they don't need install. [CHAR LIMIT=NONE] -->
    <string name="instant_apps_message">App opened without being installed.</string>

    <!-- Message of the instant apps notification indicating they don't need install, plus a link to more information. [CHAR LIMIT=NONE] -->
    <string name="instant_apps_message_with_help">App opened without being installed. Tap to learn more.</string>

    <!-- URL of the webpage that explains instant apps. -->
    <string name="instant_apps_help_url" translatable="false"></string>

    <!-- Action label for launching app info on the specified app [CHAR LIMIT=20] -->
    <string name="app_info">App info</string>

    <!-- Action label for switching to a browser for an instant app [CHAR LIMIT=20] -->
    <string name="go_to_web">Go to browser</string>

    <!-- Quick settings tile for toggling mobile data [CHAR LIMIT=20] -->
    <string name="mobile_data">Mobile data</string>

    <!-- Quick settings tile secondary label format combining roaming with the mobile data type. [CHAR LIMIT=NONE] -->
    <string name="mobile_data_text_format"><xliff:g name="roaming_status" example="Roaming">%1$s</xliff:g> \u2014 <xliff:g name="mobile_data_type" example="LTE">%2$s</xliff:g></string>

    <!-- Quick settings tile secondary label format combining carrier name with the mobile data tye. [CHAR LIMIT=NONE] -->
    <string name="mobile_carrier_text_format"><xliff:g id="carrier_name" example="Test">%1$s</xliff:g>, <xliff:g id="mobile_data_type" example="LTE">%2$s</xliff:g></string>

    <!-- Label for when wifi is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="wifi_is_off">Wi-Fi is off</string>

    <!-- Label for when bluetooth is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="bt_is_off">Bluetooth is off</string>

    <!-- Label for when Do not disturb is off in QS detail panel [CHAR LIMIT=NONE] -->
    <string name="dnd_is_off">Do Not Disturb is off</string>

    <!-- Label for when Do not disturb is on in lockscreen quick affordance [CHAR LIMIT=NONE] -->
    <string name="dnd_is_on">Do Not Disturb is on</string>

    <!-- Prompt for when Do not disturb is on from automatic rule in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_auto_rule">Do Not Disturb was turned on by an automatic rule (<xliff:g name="rule">%s</xliff:g>).</string>

    <!-- Prompt for when Do not disturb is on from app in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_app">Do Not Disturb was turned on by an app (<xliff:g name="app">%s</xliff:g>).</string>

    <!-- Prompt for when Do not disturb is on from automatic rule or app in QS [CHAR LIMIT=NONE] -->
    <string name="qs_dnd_prompt_auto_rule_app">Do Not Disturb was turned on by an automatic rule or app.</string>

    <!-- Title of the "running foreground services" dialog. [CHAR LIMIT=NONE] -->
    <string name="running_foreground_services_title">Apps running in background</string>

    <!-- Descriptive text of an item in the "running foreground services" dialog, telling the
        user what will happen when they tap on that item (which is an application that has
        been identified for them as running). [CHAR LIMIT=NONE] -->
    <string name="running_foreground_services_msg">Tap for details on battery and data usage</string>

    <!-- Title of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_title">Turn off mobile data?</string>

    <!-- Message body of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_message">You won\’t have access to data or the internet through <xliff:g id="carrier" example="T-Mobile">%s</xliff:g>. Internet will only be available via Wi-Fi.</string>

    <!-- Text used to refer to the user's current carrier in mobile_data_disable_message if the users's mobile network carrier name is not available [CHAR LIMIT=NONE] -->
    <string name="mobile_data_disable_message_default_carrier">your carrier</string>

    <!-- Title of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="auto_data_switch_disable_title">Switch back to <xliff:g id="carrier" example="T-Mobile">%s</xliff:g>?</string>
    <!-- Message body of the dialog to turn off data usage [CHAR LIMIT=NONE] -->
    <string name="auto_data_switch_disable_message">Mobile data won\’t automatically switch based on availability</string>
    <!-- Negative button title of the quick settings switch back to DDS dialog [CHAR LIMIT=NONE] -->
    <string name="auto_data_switch_dialog_negative_button">No thanks</string>
    <!-- Positive button title of the quick settings switch back to DDS dialog [CHAR LIMIT=NONE] -->
    <string name="auto_data_switch_dialog_positive_button">Yes, switch</string>

    <!-- Warning shown when user input has been blocked due to another app overlaying screen
     content. Since we don't know what the app is showing on top of the input target, we
     can't verify user consent. [CHAR LIMIT=NONE] -->
    <string name="touch_filtered_warning">Because an app is obscuring a permission request, Settings
        can’t verify your response.</string>

    <!-- Title of prompt requesting access to display slices [CHAR LIMIT=NONE] -->
    <string name="slice_permission_title">Allow <xliff:g id="app" example="Example App">%1$s</xliff:g> to show <xliff:g id="app_2" example="Other Example App">%2$s</xliff:g> slices?</string>

    <!-- Description of what kind of access is given to a slice host [CHAR LIMIT=NONE] -->
    <string name="slice_permission_text_1"> - It can read information from <xliff:g id="app" example="Example App">%1$s</xliff:g></string>
    <!-- Description of what kind of access is given to a slice host [CHAR LIMIT=NONE] -->
    <string name="slice_permission_text_2"> - It can take actions inside <xliff:g id="app" example="Example App">%1$s</xliff:g></string>

    <!-- Text on checkbox allowing the app to show slices from all apps [CHAR LIMIT=NONE] -->
    <string name="slice_permission_checkbox">Allow <xliff:g id="app" example="Example App">%1$s</xliff:g> to show slices from any app</string>

    <!-- Option to grant the slice permission request on the screen [CHAR LIMIT=15] -->
    <string name="slice_permission_allow">Allow</string>

    <!-- Option to grant the slice permission request on the screen [CHAR LIMIT=15] -->
    <string name="slice_permission_deny">Deny</string>

    <!-- List of packages for which we don't want to show recents onboarding, add into overlay as needed. -->

    <!-- The title of the notification to suggest enabling automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="auto_saver_title">Tap to schedule Battery Saver</string>

    <!-- The content of the notification to suggest enabling automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="auto_saver_text">Turn on when battery is likely to run out</string>

    <!-- An action on the notification to suggest enabling automatic battery saver: Do not turn on automatic battery saver.  [CHAR LIMIT=NONE]-->
    <string name="no_auto_saver_action">No thanks</string>

    <!-- The title of the dialog that tells that scheduled (i.e. automatic) battery saver has been turned on. [CHAR LIMIT=NONE]-->

    <!-- The content of the dialog that tells that scheduled (i.e. automatic) battery saver has been turned on. [CHAR LIMIT=NONE]-->

    <!-- An action on the dialog that tells that scheduled (i.e. automatic) battery saver: open the battery saver setting.  [CHAR LIMIT=NONE]-->

    <!-- An action on the dialog that tells that scheduled (i.e. automatic) battery saver: user acknowledges and closes the dialog.  [CHAR LIMIT=NONE]-->

    <!-- URl of the webpage that explains battery saver. -->
    <string name="help_uri_battery_saver_learn_more_link_target" translatable="false"></string>

    <!-- Title for the privacy indicators dialog, only appears as part of a11y descriptions [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_a11y_title">In use</string>

    <!-- Content description for ongoing privacy chip. Use with multiple apps [CHAR LIMIT=NONE]-->
    <string name="ongoing_privacy_chip_content_multiple_apps">Applications are using your <xliff:g id="types_list" example="camera, location">%s</xliff:g>.</string>

    <!-- Separator for types. Include spaces before and after if needed [CHAR LIMIT=10] -->
    <string name="ongoing_privacy_dialog_separator">,\u0020</string>

    <!-- Separator for types, before last type. Include spaces before and after if needed [CHAR LIMIT=10] -->
    <string name="ongoing_privacy_dialog_last_separator">\u0020and\u0020</string>

    <!-- Text for privacy dialog, using sensitive app op (one of camera, location, microphone) right now [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_using_op">Being used by <xliff:g id="application_name" example="Photos">%1$s</xliff:g></string>

    <!-- Text for privacy dialog, recently used sensitive app op (one of camera, location, microphone) [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_recent_op">Recently used by <xliff:g id="application_name" example="Photos">%1$s</xliff:g></string>

    <!-- Text for privacy dialog, indicating that the application is the enterprise version [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_enterprise">(work)</string>

    <!-- Text for privacy dialog, identifying the phone call app [CHAR LIMIT=NONE]-->
    <string name="ongoing_privacy_dialog_phonecall">Phone call</string>

    <!-- Text for privacy dialog, indicating that an app (or multiple) is using an op on behalf of another [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_attribution_text">(through <xliff:g id="application name(s)" example="Maps, and Assistant">%s</xliff:g>)</string>

    <!-- Text for privacy dialog, displaying just the subattribution label [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_attribution_label">(<xliff:g id="attribution_label" example="For Wallet">%s</xliff:g>)</string>

    <!-- Text for privacy dialog, displaying both subattribution and proxy label [CHAR LIMIT=NONE] -->
    <string name="ongoing_privacy_dialog_attribution_proxy_label">(<xliff:g id="attribution_label" example="For Wallet">%1$s</xliff:g>  \u2022 <xliff:g id="proxy_label" example="Maps, and Assistant">%2$s</xliff:g>)</string>

    <!-- Text for camera app op [CHAR LIMIT=20]-->
    <string name="privacy_type_camera">camera</string>

    <!-- Text for location app op [CHAR LIMIT=20]-->
    <string name="privacy_type_location">location</string>

    <!-- Text for microphone app op [CHAR LIMIT=20]-->
    <string name="privacy_type_microphone">microphone</string>

    <!-- Text for media projection privacy type [CHAR LIMIT=20]-->
    <string name="privacy_type_media_projection">screen recording</string>

    <!-- What to show on the ambient display player when song doesn't have a title. [CHAR LIMIT=20] -->
    <string name="music_controls_no_title">No title</string>

    <!-- Title of the overlay warning the user to interact with the device or it will go to sleep. [CHAR LIMIT=25] -->
    <string name="inattentive_sleep_warning_title">Standby</string>

    <!-- Font scaling -->
    <!-- Font scaling: Quick Settings dialog title [CHAR LIMIT=30] -->
    <string name="font_scaling_dialog_title">Font Size</string>
    <!-- Content Description for the icon button to make fonts smaller. [CHAR LIMIT=30] -->
    <string name="font_scaling_smaller">Make smaller</string>
    <!-- Content Description for the icon button to make fonts larger. [CHAR LIMIT=30] -->
    <string name="font_scaling_larger">Make larger</string>

    <!-- Window Magnification strings -->
    <!-- Title for Magnification Window [CHAR LIMIT=NONE] -->
    <string name="magnification_window_title">Magnification Window</string>
    <!-- Title for Magnification Controls Window [CHAR LIMIT=NONE] -->
    <string name="magnification_controls_title">Magnification Window Controls</string>
    <!-- Action in accessibility menu to zoom in content of the magnification window. [CHAR LIMIT=30] -->
    <string name="accessibility_control_zoom_in">Zoom in</string>
    <!-- Action in accessibility menu to zoom out content of the magnification window. [CHAR LIMIT=30] -->
    <string name="accessibility_control_zoom_out">Zoom out</string>
    <!-- Action in accessibility menu to move the magnification window up. [CHAR LIMIT=30] -->
    <string name="accessibility_control_move_up">Move up</string>
    <!-- Action in accessibility menu to move the magnification window down. [CHAR LIMIT=30] -->
    <string name="accessibility_control_move_down">Move down</string>
    <!-- Action in accessibility menu to move the magnification window left. [CHAR LIMIT=30] -->
    <string name="accessibility_control_move_left">Move left</string>
    <!-- Action in accessibility menu to move the magnification window right. [CHAR LIMIT=30] -->
    <string name="accessibility_control_move_right">Move right</string>

    <!-- Action in accessibility menu to increase the magnification window width. [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_increase_window_width">Increase width of magnifier</string>
    <!-- Action in accessibility menu to decrease the magnification window width. [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_decrease_window_width">Decrease width of magnifier</string>
    <!-- Action in accessibility menu to increase the magnification window height. [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_increase_window_height">Increase height of magnifier</string>
    <!-- Action in accessibility menu to decrease the magnification window height. [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_decrease_window_height">Decrease height of magnifier</string>

    <!-- Content description for magnification mode switch. [CHAR LIMIT=NONE] -->
    <string name="magnification_mode_switch_description">Magnification switch</string>
    <!-- A11y state description for magnification mode switch that device is in full-screen mode. [CHAR LIMIT=NONE] -->
    <string name="magnification_mode_switch_state_full_screen">Magnify full screen</string>
    <!-- A11y state description for magnification mode switch that device is in window mode. [CHAR LIMIT=NONE] -->
    <string name="magnification_mode_switch_state_window">Magnify part of screen</string>
    <!-- Click action label for magnification settings panel. [CHAR LIMIT=NONE] -->
    <string name="magnification_open_settings_click_label">Open magnification settings</string>
    <!-- Click action label for magnification settings panel. [CHAR LIMIT=NONE] -->
    <string name="magnification_close_settings_click_label">Close magnification settings</string>
    <!-- Click action label for exiting magnifier edit mode. [CHAR LIMIT=NONE] -->
    <string name="magnification_exit_edit_mode_click_label">Exit edit mode</string>
    <!-- Label of the corner of a rectangle that you can tap and drag to resize the magnification area. [CHAR LIMIT=NONE] -->
    <string name="magnification_drag_corner_to_resize">Drag corner to resize</string>

    <!-- Title of the magnification option button allow diagonal scrolling [CHAR LIMIT=NONE]-->
    <string name="accessibility_allow_diagonal_scrolling">Allow diagonal scrolling</string>
    <!-- Title of the magnification option button Resize [CHAR LIMIT=NONE]-->
    <string name="accessibility_resize">Resize</string>
    <!-- Title of the magnification option button Change type [CHAR LIMIT=NONE]-->
    <string name="accessibility_change_magnification_type">Change magnification type</string>
    <!-- Title of the magnification option button End resizing [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_end_resizing">End resizing</string>

    <!-- Description of the window magnification Top handle [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_top_handle">Top handle</string>
    <!-- Description of the window magnification Left handle [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_left_handle">Left handle</string>
    <!-- Description of the window magnification Right handle [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_right_handle">Right handle</string>
    <!-- Description of the window magnification Bottom handle [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_bottom_handle">Bottom handle</string>

    <!-- Description of the window magnification panel [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_settings_panel_description">Magnification settings</string>
    <!-- Title of the window magnification panel option Magnifier size [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnifier_size">Magnifier size</string>
    <!-- Title of the window magnification panel option Zoom [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_zoom">Zoom</string>
    <!-- Click action label for magnification panel medium size [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_medium">Medium</string>
    <!-- Click action label for magnification panel small size [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_small">Small</string>
    <!-- Click action label for magnification panel large size [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_large">Large</string>
    <!-- Click action label for magnification panel full screen size [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_fullscreen">Full screen</string>
    <!-- Click action label for magnification panel Done [CHAR LIMIT=20]-->
    <string name="accessibility_magnification_done">Done</string>
    <!-- Click action label for edit magnification size [CHAR LIMIT=20]-->
    <string name="accessibility_magnifier_edit">Edit</string>
    <!-- Click action label for magnification panel settings [CHAR LIMIT=NONE]-->
    <string name="accessibility_magnification_magnifier_window_settings">Magnifier window settings</string>

    <!-- Accessibility floating menu strings -->
    <!-- Message for the accessibility floating button migration tooltip. It shows when the user use gestural navigation then upgrade their system. It will tell the user they could customize or replace the floating button in Settings. [CHAR LIMIT=100] -->
    <string name="accessibility_floating_button_migration_tooltip">Tap to open accessibility features. Customize or replace this button in Settings.\n\n<annotation id="link">View settings</annotation></string>
    <!-- Message for the accessibility floating button docking tooltip. It shows when the user first time drag the button. It will tell the user about docking behavior. [CHAR LIMIT=70] -->
    <string name="accessibility_floating_button_docking_tooltip">Move button to the edge to hide it temporarily</string>
    <!-- Text for the undo action button of the message view of the accessibility floating menu to perform undo operation. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_undo">Undo</string>
    <!-- Notification title shown when accessibility floating button is in hidden state. [CHAR LIMIT=NONE] -->
    <string name="accessibility_floating_button_hidden_notification_title">Accessibility button hidden</string>
    <!-- Notification content text to explain user can tap notification to bring back accessibility floating button. [CHAR LIMIT=NONE] -->
    <string name="accessibility_floating_button_hidden_notification_text">Tap to show accessibility button</string>

    <!-- Text for the message view with undo action of the accessibility floating menu to show which feature shortcut was removed. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_undo_message_label_text"><xliff:g id="feature name" example="Magnification">%s</xliff:g> shortcut removed</string>
    <!-- Text for the message view with undo action of the accessibility floating menu to show how many features shortcuts were removed. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_undo_message_number_text">{count, plural,
        =1 {# shortcut removed}
        other {# shortcuts removed}
    }</string>
    <!-- Action in accessibility menu to move the accessibility floating button to the top left of the screen. [CHAR LIMIT=30] -->
    <string name="accessibility_floating_button_action_move_top_left">Move top left</string>
    <!-- Action in accessibility menu to move the accessibility floating button to the top right of the screen. [CHAR LIMIT=30] -->
    <string name="accessibility_floating_button_action_move_top_right">Move top right</string>
    <!-- Action in accessibility menu to move the accessibility floating button to the bottom left of the screen. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_action_move_bottom_left">Move bottom left</string>
    <!-- Action in accessibility menu to move the accessibility floating button to the bottom right of the screen. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_action_move_bottom_right">Move bottom right</string>
    <!-- Action in accessibility menu to move the accessibility floating button to the edge and hide it to half. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_action_move_to_edge_and_hide_to_half">Move to edge and hide</string>
    <!-- Action in accessibility menu to move the accessibility floating button out the edge and show. [CHAR LIMIT=36]-->
    <string name="accessibility_floating_button_action_move_out_edge_and_show">Move out edge and show</string>
    <!-- Action in accessibility menu to remove the accessibility floating menu view on the screen. [CHAR LIMIT=36]-->
    <string name="accessibility_floating_button_action_remove_menu">Remove</string>
    <!-- Action in accessibility menu to toggle on/off the accessibility feature. [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_action_double_tap_to_toggle">toggle</string>
    <!-- Action in accessibility menu to open the shortcut edit menu" [CHAR LIMIT=30]-->
    <string name="accessibility_floating_button_action_edit">Edit</string>

    <!-- Device Controls strings -->
    <!-- Device Controls, Quick Settings tile title [CHAR LIMIT=30] -->
    <string name="quick_controls_title">Device controls</string>

    <!-- Controls management providers screen title [CHAR LIMIT=60]-->
    <string name="controls_providers_title">Choose app to add controls</string>
    <!-- Number of favorites for controls management screen [CHAR LIMIT=NONE]-->
    <string name="controls_number_of_favorites">{count, plural,
        =1 {# control added.}
        other {# controls added.}
    }</string>

    <!-- Removed control in management screen [CHAR LIMIT=20] -->
    <string name="controls_removed">Removed</string>

    <!-- Title for the dialog presented to the user to authorize this app to display a Device
         controls panel (embedded activity) instead of controls rendered by SystemUI [CHAR LIMIT=30] -->
    <string name="controls_panel_authorization_title">Add <xliff:g id="appName" example="My app">%s</xliff:g>?</string>

    <!-- Shows in a dialog presented to the user to authorize this app to display a Device controls
         panel (embedded activity) instead of controls rendered by SystemUI [CHAR LIMIT=NONE] -->
    <string name="controls_panel_authorization"><xliff:g id="appName" example="My app">%s</xliff:g> can choose which controls and content show here.</string>

    <!-- Shows in a dialog presented to the user to authorize this app removal from a Device
         controls panel [CHAR LIMIT=NONE] -->
    <string name="controls_panel_remove_app_authorization">Remove controls for <xliff:g example="My app" id="appName">%s</xliff:g>?</string>

    <!-- a11y state description for a control that is currently favorited [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_favorite">Favorited</string>
    <!-- a11y state description for a control that is currently favorited with its position [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_favorite_position">Favorited, position <xliff:g id="number" example="1">%d</xliff:g></string>
    <!-- a11y state description for a control that is currently not favorited [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_not_favorite">Unfavorited</string>
    <!-- a11y action to favorite a control. It will read as "Double-tap to favorite" in screen readers [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_change_favorite">favorite</string>
    <!-- a11y action to unfavorite a control. It will read as "Double-tap to unfavorite" in screen readers [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_change_unfavorite">unfavorite</string>
    <!-- a11y action to move a control to the position specified by the parameter [CHAR LIMIT=NONE] -->
    <string name="accessibility_control_move">Move to position <xliff:g id="number" example="1">%d</xliff:g></string>

    <!-- Controls management controls screen default title [CHAR LIMIT=30] -->
    <string name="controls_favorite_default_title">Controls</string>
    <!-- Controls management controls screen subtitle [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_subtitle">Choose device controls to access quickly</string>
    <!-- Controls management editing screen, user direction for rearranging controls [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_rearrange">Hold &amp; drag to rearrange controls</string>

    <!-- Controls management editing screen, text to indicate that all the favorites have been removed [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_removed">All controls removed</string>

    <!-- Controls management favorites screen, See other apps with changes made [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_toast_no_changes">Changes not saved</string>

    <!-- Controls management favorites screen. See other apps button [CHAR LIMIT=30] -->
    <string name="controls_favorite_see_other_apps">See other apps</string>

    <!-- Controls management favorites screen. Rearrange controls button [CHAR LIMIT=30]-->
    <string name="controls_favorite_rearrange_button">Rearrange</string>

    <!-- Controls management edit screen. Add controls button [CHAR LIMIT=30]-->
    <string name="controls_favorite_add_controls">Add controls</string>

    <!-- Controls management edit screen. Return to editing button [CHAR LIMIT=30]-->
    <string name="controls_favorite_back_to_editing">Back to editing</string>

    <!-- Controls management controls screen error on load message [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_load_error">Controls could not be loaded. Check the <xliff:g id="app" example="System UI">%s</xliff:g> app to make sure that the app settings haven\u2019t changed.</string>
    <!-- Controls management controls screen no controls found on load message [CHAR LIMIT=NONE] -->
    <string name="controls_favorite_load_none">Compatible controls unavailable</string>
    <!-- Controls management controls screen header for Other zone [CHAR LIMIT=60] -->
    <string name="controls_favorite_other_zone_header">Other</string>

    <!-- Controls dialog title [CHAR LIMIT=40] -->
    <string name="controls_dialog_title">Add to device controls</string>
    <!-- Controls dialog add to favorites [CHAR LIMIT=40] -->
    <string name="controls_dialog_ok">Add</string>
    <!-- Controls dialog remove app from a panel [CHAR LIMIT=40] -->
    <string name="controls_dialog_remove">Remove</string>
    <!-- Controls dialog message. Indicates app that suggested this control [CHAR LIMIT=NONE] -->
    <string name="controls_dialog_message">Suggested by <xliff:g id="app" example="System UI">%s</xliff:g></string>
    <!-- Controls tile secondary label when device is locked and user does not want access to controls from lockscreen [CHAR LIMIT=20] -->
    <string name="controls_tile_locked">Device locked</string>

    <!-- Title of the dialog to show and control devices from lock screen [CHAR LIMIT=NONE] -->
    <string name="controls_settings_show_controls_dialog_title">Show and control devices from lock screen?</string>
    <!-- Message of the dialog to show and control devices from lock screen [CHAR LIMIT=NONE] -->
    <string name="controls_settings_show_controls_dialog_message">You can add controls for your external devices to the lock screen.\n\nYour device app may allow you to control some devices without unlocking your phone or tablet.\n\nYou can make changes any time in Settings.</string>
    <!-- Title of the dialog to control certain devices from lock screen without auth [CHAR LIMIT=NONE] -->
    <string name="controls_settings_trivial_controls_dialog_title">Control devices from lock screen?</string>
    <!-- Message of the dialog to control certain devices from lock screen without auth [CHAR LIMIT=NONE] -->
    <string name="controls_settings_trivial_controls_dialog_message">You can control some devices without unlocking your phone or tablet. Your device app determines which devices can be controlled in this way.</string>
    <!-- Neutral button title of the controls dialog [CHAR LIMIT=NONE] -->
    <string name="controls_settings_dialog_neutral_button">No thanks</string>
    <!-- Positive button title of the controls dialog  [CHAR LIMIT=NONE] -->
    <string name="controls_settings_dialog_positive_button">Yes</string>

    <!-- Controls PIN entry dialog, switch to alphanumeric keyboard [CHAR LIMIT=100] -->
    <string name="controls_pin_use_alphanumeric">PIN contains letters or symbols</string>
    <!-- Controls PIN entry dialog, title [CHAR LIMIT=30] -->
    <string name="controls_pin_verify">Verify <xliff:g id="device" example="Backdoor lock">%s</xliff:g></string>
    <!-- Controls PIN entry dialog, title when 1st attempt failed [CHAR LIMIT=30] -->
    <string name="controls_pin_wrong">Wrong PIN</string>
    <!-- Controls PIN entry dialog, text hint [CHAR LIMIT=30] -->
    <string name="controls_pin_instructions">Enter PIN</string>
    <!-- Controls PIN entry dialog, text hint, retry [CHAR LIMIT=30] -->
    <string name="controls_pin_instructions_retry">Try another PIN</string>
    <!-- Controls confirmation dialog, user prompt [CHAR LIMIT=NONE] -->
    <string name="controls_confirmation_message">Confirm change for <xliff:g id="device" example="Backdoor lock">%s</xliff:g></string>

    <!-- Tooltip to show in management screen when there are multiple structures [CHAR_LIMIT=50] -->
    <string name="controls_structure_tooltip">Swipe to see more</string>

    <!-- Accessibility action informing the user how they can retry face authentication [CHAR LIMIT=NONE] -->
    <string name="retry_face">Retry face authentication</string>

    <!-- Message to tell the user to wait while systemui attempts to load a set of
         recommended controls [CHAR_LIMIT=60] -->
    <string name="controls_seeding_in_progress">Loading recommendations</string>

    <!-- Title for media controls [CHAR_LIMIT=50] -->
    <string name="controls_media_title">Media</string>
    <!-- Explanation for closing controls associated with a specific media session [CHAR_LIMIT=50] -->
    <string name="controls_media_close_session">Hide this media control for <xliff:g id="app_name" example="Foo Music App">%1$s</xliff:g>?</string>
    <!-- Explanation that controls associated with a specific media session are active [CHAR_LIMIT=50] -->
    <string name="controls_media_active_session">The current media session cannot be hidden.</string>
    <!-- Label for a button that will hide media controls [CHAR_LIMIT=30] -->
    <string name="controls_media_dismiss_button">Hide</string>
    <!-- Label for button to resume media playback [CHAR_LIMIT=NONE] -->
    <string name="controls_media_resume">Resume</string>
    <!-- Label for button to go to media control settings screen [CHAR_LIMIT=30] -->
    <string name="controls_media_settings_button">Settings</string>
    <!-- Description for media control's playing media item, including information for the media's title, the artist, and source app [CHAR LIMIT=NONE]-->
    <string name="controls_media_playing_item_description"><xliff:g id="song_name" example="Daily mix">%1$s</xliff:g> by <xliff:g id="artist_name" example="Various artists">%2$s</xliff:g> is playing from <xliff:g id="app_label" example="Spotify">%3$s</xliff:g></string>
    <!-- Content description for media cotnrols progress bar [CHAR_LIMIT=NONE] -->
    <string name="controls_media_seekbar_description"><xliff:g id="elapsed_time" example="1:30">%1$s</xliff:g> of <xliff:g id="total_time" example="3:00">%2$s</xliff:g></string>
    <!-- Placeholder title to inform user that an app has posted media controls [CHAR_LIMIT=NONE] -->
    <string name="controls_media_empty_title"><xliff:g id="app_name" example="Foo Music App">%1$s</xliff:g> is running</string>

    <!-- Description for button in media controls. Pressing button starts playback [CHAR_LIMIT=NONE] -->
    <string name="controls_media_button_play">Play</string>
    <!-- Description for button in media controls. Pressing button pauses playback [CHAR_LIMIT=NONE] -->
    <string name="controls_media_button_pause">Pause</string>
    <!-- Description for button in media controls. Pressing button goes to previous track [CHAR_LIMIT=NONE] -->
    <string name="controls_media_button_prev">Previous track</string>
    <!-- Description for button in media controls. Pressing button goes to next track [CHAR_LIMIT=NONE] -->
    <string name="controls_media_button_next">Next track</string>
    <!-- Description for button in media controls. Used when media is connecting to a remote device (via something like chromecast). Pressing button does nothing [CHAR_LIMIT=NONE] -->
    <string name="controls_media_button_connecting">Connecting</string>

    <!-- Title for Smartspace recommendation card within media controls. The "Play" means the action to play a media [CHAR_LIMIT=10] -->
    <string name="controls_media_smartspace_rec_title">Play</string>
    <!-- Description for Smartspace recommendation card within media controls [CHAR_LIMIT=NONE] -->
    <string name="controls_media_smartspace_rec_description">Open <xliff:g id="app_label" example="Spotify">%1$s</xliff:g></string>
    <!-- Description for Smartspace recommendation's media item, including information for the media's title, the artist, and source app [CHAR LIMIT=NONE]-->
    <string name="controls_media_smartspace_rec_item_description">Play <xliff:g id="song_name" example="Daily mix">%1$s</xliff:g> by <xliff:g id="artist_name" example="Various artists">%2$s</xliff:g> from <xliff:g id="app_label" example="Spotify">%3$s</xliff:g></string>
    <!-- Description for Smartspace recommendation's media item which doesn't have artist info, including information for the media's title and the source app [CHAR LIMIT=NONE]-->
    <string name="controls_media_smartspace_rec_item_no_artist_description">Play <xliff:g id="song_name" example="Daily mix">%1$s</xliff:g> from <xliff:g id="app_label" example="Spotify">%2$s</xliff:g></string>
    <!-- Header title for Smartspace recommendation card within media controls. [CHAR_LIMIT=30] -->
    <string name="controls_media_smartspace_rec_header">For You</string>

    <!--- ****** Media tap-to-transfer ****** -->
    <!-- Text for a button to undo the media transfer. [CHAR LIMIT=20] -->
    <string name="media_transfer_undo">Undo</string>
    <!-- Text to ask the user to move their device closer to a different device (deviceName) in order to play media on the different device. [CHAR LIMIT=75] -->
    <string name="media_move_closer_to_start_cast">Move closer to play on <xliff:g id="deviceName" example="My Tablet">%1$s</xliff:g></string>
    <!-- Text to ask the user to move their device closer to a different device (deviceName) in order to transfer media from the different device and back onto the current device. [CHAR LIMIT=75] -->
    <string name="media_move_closer_to_end_cast">To play here, move closer to <xliff:g id="deviceName" example="tablet">%1$s</xliff:g></string>
    <!-- Text informing the user that their media is now playing on a different device (deviceName). [CHAR LIMIT=50] -->
    <string name="media_transfer_playing_different_device">Playing on <xliff:g id="deviceName" example="My Tablet">%1$s</xliff:g></string>
    <!-- Text informing the user that the media transfer has failed because something went wrong. [CHAR LIMIT=50] -->
    <string name="media_transfer_failed">Something went wrong. Try again.</string>
    <!-- Text to indicate that a media transfer is currently in-progress, aka loading. [CHAR LIMIT=NONE] -->
    <string name="media_transfer_loading">Loading</string>
    <!-- Default name of the device. [CHAR LIMIT=30] -->
    <string name="media_ttt_default_device_type">tablet</string>
    <!-- Description of media transfer icon of unknown app appears in receiver devices. [CHAR LIMIT=NONE]-->
    <string name="media_transfer_receiver_content_description_unknown_app">Casting your media</string>
    <!-- Description of media transfer icon appears in receiver devices. [CHAR LIMIT=NONE]-->
    <string name="media_transfer_receiver_content_description_with_app_name">Casting <xliff:g id="app_label" example="Spotify">%1$s</xliff:g></string>

    <!-- Error message indicating that a control timed out while waiting for an update [CHAR_LIMIT=30] -->
    <string name="controls_error_timeout">Inactive, check app</string>
    <!-- Error message indicating that the control is no longer available in the application [CHAR LIMIT=30] -->
    <string name="controls_error_removed">Not found</string>
    <!-- Title for dialog indicating that the control is no longer available in the application [CHAR LIMIT=30] -->
    <string name="controls_error_removed_title">Control is unavailable</string>
    <!-- Message body for dialog indicating that the control is no longer available in the application [CHAR LIMIT=NONE] -->
    <string name="controls_error_removed_message">Couldn\u2019t access <xliff:g id="device" example="Backdoor lock">%1$s</xliff:g>. Check the <xliff:g id="application" example="Google Home">%2$s</xliff:g> app to make sure the control is still available and that the app settings haven\u2019t changed.</string>
    <!-- Text for button to open the corresponding application [CHAR_LIMIT=20] -->
    <string name="controls_open_app">Open app</string>
    <!-- Error message indicating that an unspecified error occurred while getting the status [CHAR LIMIT=30] -->
    <string name="controls_error_generic">Can\u2019t load status</string>
    <!-- Error message indicating that a control action failed [CHAR_LIMIT=30] -->
    <string name="controls_error_failed">Error, try again</string>
    <!-- Controls menu, add [CHAR_LIMIT=30] -->
    <string name="controls_menu_add">Add controls</string>
    <!-- Controls menu, edit [CHAR_LIMIT=30] -->
    <string name="controls_menu_edit">Edit controls</string>
    <!-- Controls menu, add another app [CHAR LIMIT=30] -->
    <string name="controls_menu_add_another_app">Add app</string>
    <!-- Controls menu, remove app [CHAR_LIMIT=30] -->
    <string name="controls_menu_remove">Remove app</string>

    <!-- Title for the media output dialog with media related devices [CHAR LIMIT=50] -->
    <string name="media_output_dialog_add_output">Add outputs</string>
    <!-- Title for the media output slice with group devices [CHAR LIMIT=50] -->
    <string name="media_output_dialog_group">Group</string>
    <!-- Summary for media output group with only one device which is active [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_single_device">1 device selected</string>
    <!-- Summary for media output group with the active device count [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_multiple_devices"><xliff:g id="count" example="2">%1$d</xliff:g> devices selected</string>
    <!-- Summary for disconnected status [CHAR LIMIT=50] -->
    <string name="media_output_dialog_disconnected">(disconnected)</string>
    <!-- Summary for connecting error message [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_connect_failed">Can\'t switch. Tap to try again.</string>
    <!-- Title for connecting item [CHAR LIMIT=60] -->
    <string name="media_output_dialog_pairing_new">Connect a device</string>
    <!-- Title for launch app [CHAR LIMIT=60] -->
    <string name="media_output_dialog_launch_app_text">To cast this session, please open the app.</string>
    <!-- App name when can't get app name [CHAR LIMIT=60] -->
    <string name="media_output_dialog_unknown_launch_app_name">Unknown app</string>
    <!-- Button text for stopping casting [CHAR LIMIT=60] -->
    <string name="media_output_dialog_button_stop_casting">Stop casting</string>
    <!-- Accessibility text describing purpose of media output dialog. [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_accessibility_title">Available devices for audio output.</string>
    <!-- Accessibility text describing purpose of seekbar in media output dialog. [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_accessibility_seekbar">Volume</string>
    <!-- Summary for media output volume of a device in percentage [CHAR LIMIT=NONE] -->
    <string name="media_output_dialog_volume_percentage"><xliff:g id="percentage" example="10">%1$d</xliff:g>%%</string>
    <!-- Title for Speakers and Displays group. [CHAR LIMIT=NONE] -->
    <string name="media_output_group_title_speakers_and_displays">Speakers &amp; Displays</string>
    <!-- Title for Suggested Devices group. [CHAR LIMIT=NONE] -->
    <string name="media_output_group_title_suggested_device">Suggested Devices</string>
    <!-- Summary for end session dialog. [CHAR LIMIT=NONE] -->
    <string name="media_output_end_session_dialog_summary">Stop your shared session to move media to another device</string>
    <!-- Button text for stopping session [CHAR LIMIT=60] -->
    <string name="media_output_end_session_dialog_stop">Stop</string>


    <!-- Media Output Broadcast Dialog -->
    <!-- Title for Broadcast First Notify Dialog [CHAR LIMIT=60] -->
    <string name="media_output_first_broadcast_title">How broadcasting works</string>
    <!-- Title for Broadcast Notify Dialog [CHAR LIMIT=60] -->
    <string name="media_output_broadcast">Broadcast</string>
    <!-- Message for notifying the user about the Broadcast at first launch [CHAR LIMIT=NONE] -->
    <string name="media_output_first_notify_broadcast_message">People near you with compatible Bluetooth devices can listen to the media you\'re broadcasting</string>
    <!-- Message for Broadcasting information and QR code [CHAR LIMIT=NONE] -->
    <string name="media_output_broadcasting_message">To listen to your broadcast, people nearby with compatible Bluetooth devices can scan your QR code or use your broadcast name and password</string>
    <!-- Title for Broadcast Name [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_name">Broadcast Name</string>
    <!-- Title for Broadcast Code(Password) [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_code">Password</string>
    <!-- Button for change broadcast name and broadcast code [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_dialog_save">Save</string>
    <!-- The "starting" text when Broadcast is starting [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_starting">Starting&#8230;</string>
    <!-- The button text when Broadcast start failed [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_start_failed">Can\u2019t broadcast</string>
    <!-- The error message when Broadcast name/code update failed [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_update_error">Can\u2019t save. Try again.</string>
    <!-- The error message when Broadcast name/code update failed and can't change again[CHAR LIMIT=60] -->
    <string name="media_output_broadcast_last_update_error">Can\u2019t save.</string>
    <!-- The hint message when Broadcast code is less than 4 characters [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_code_hint_no_less_than_min">Use at least 4 characters</string>
    <!-- The hint message when Broadcast edit is more than 16/254 characters [CHAR LIMIT=60] -->
    <string name="media_output_broadcast_edit_hint_no_more_than_max">Use fewer than <xliff:g id="length" example="16">%1$d</xliff:g> characters</string>

    <!-- Label for clip data when copying the build number off QS [CHAR LIMIT=NONE]-->
    <string name="build_number_clip_data_label">Build number</string>
    <!-- Text to display when copying the build number off QS [CHAR LIMIT=NONE]-->
    <string name="build_number_copy_toast">Build number copied to clipboard.</string>

     <!-- Status for conversation without interaction data [CHAR LIMIT=120] -->
    <string name="basic_status">Open conversation</string>
    <!--Title text for Conversation widget set up screen [CHAR LIMIT=180] -->
    <string name="select_conversation_title">Conversation widgets</string>
    <!--Text explaining to tap a conversation to select it show in their Conversation widget [CHAR LIMIT=180] -->
    <string name="select_conversation_text">Tap a conversation to add it to your Home screen</string>
    <!--Text explaining there are no existing conversations to show in their Conversation widget [CHAR LIMIT=100] -->
    <string name="no_conversations_text">Your recent conversations will show up here</string>
    <!--Text header for priority conversation tiles available to be added to the home screen [CHAR LIMIT=100] -->
    <string name="priority_conversations">Priority conversations</string>
    <!--Text header for recent conversation tiles available to be added to the home screen [CHAR LIMIT=100] -->
    <string name="recent_conversations">Recent conversations</string>
    <!-- Timestamp for notification with exact time in days plural [CHAR LIMIT=25] -->
    <string name="days_timestamp"><xliff:g id="duration" example="5">%1$s</xliff:g> days ago</string>
    <!-- Timestamp for notification from one week ago[CHAR LIMIT=25] -->
    <string name="one_week_timestamp">1 week ago</string>
    <!-- Timestamp for notification from two weeks ago [CHAR LIMIT=25] -->
    <string name="two_weeks_timestamp">2 weeks ago</string>
    <!-- Timestamp for notification when over one week ago [CHAR LIMIT=25] -->
    <string name="over_one_week_timestamp">Over 1 week ago</string>
    <!-- Timestamp for notification when over two weeks ago [CHAR LIMIT=25] -->
    <string name="over_two_weeks_timestamp">Over 2 weeks ago</string>
    <!-- Status text on the Conversation widget for a birthday today [CHAR LIMIT=20] -->
    <string name="birthday_status">Birthday</string>
    <!-- Content description text on the Conversation widget for a birthday today [CHAR LIMIT=150] -->
    <string name="birthday_status_content_description">It\'s <xliff:g id="name" example="Anna">%1$s</xliff:g>\’s birthday</string>
    <!-- Status text on the Conversation widget for an upcoming birthday [CHAR LIMIT=20] -->
    <string name="upcoming_birthday_status">Birthday soon</string>
    <!-- Content description text on the Conversation widget for an upcoming birthday [CHAR LIMIT=150] -->
    <string name="upcoming_birthday_status_content_description">It\'s <xliff:g id="name" example="Anna">%1$s</xliff:g>\’s birthday soon</string>
    <!-- Status text on the Conversation widget for an anniversary [CHAR LIMIT=20] -->
    <string name="anniversary_status">Anniversary</string>
    <!-- Content description text on the Conversation widget for an anniversary [CHAR LIMIT=150] -->
    <string name="anniversary_status_content_description">It\'s <xliff:g id="name" example="Anna">%1$s</xliff:g>\’s anniversary</string>
    <!-- Status text on the Conversation widget for sharing location [CHAR LIMIT=20] -->
    <string name="location_status">Sharing location</string>
    <!-- Content description text on the Conversation widget for sharing location [CHAR LIMIT=150] -->
    <string name="location_status_content_description"><xliff:g id="name" example="Anna">%1$s</xliff:g> is sharing location</string>
    <!-- Status text on the Conversation widget for a new story posted [CHAR LIMIT=20] -->
    <string name="new_story_status">New story</string>
    <!-- Content description text on the Conversation widget for a new story posted on social channels, usually a photograph. [CHAR LIMIT=150] -->
    <string name="new_story_status_content_description"><xliff:g id="name" example="Anna">%1$s</xliff:g> shared a new story</string>
    <!-- Status text on the Conversation widget for watching a video [CHAR LIMIT=20] -->
    <string name="video_status">Watching</string>
    <!-- Status text on the Conversation widget for listening to audio [CHAR LIMIT=20] -->
    <string name="audio_status">Listening</string>
    <!-- Status text on the Conversation widget for playing a game [CHAR LIMIT=20] -->
    <string name="game_status">Playing</string>
    <!-- Empty user name before user has selected a friend for their Conversation widget [CHAR LIMIT=10] -->
    <string name="empty_user_name">Friends</string>
    <!-- Empty status shown before user has selected a friend for their Conversation widget [CHAR LIMIT=20] -->
    <string name="empty_status">Let’s chat tonight!</string>
    <!-- Empty status shown temporarily before we've been able to load the conversation for their Conversation widget after reboots [CHAR LIMIT=40] -->
    <string name="status_before_loading">Content will show up soon</string>
    <!-- Default text for missed call notifications on their Conversation widget [CHAR LIMIT=20] -->
    <string name="missed_call">Missed call</string>
    <!-- Text when a Notification may have more messages than the number indicated [CHAR LIMIT=5] -->
    <string name="messages_count_overflow_indicator"><xliff:g id="number" example="7">%d</xliff:g>+</string>
    <!-- Description text for adding a Conversation widget [CHAR LIMIT=100] -->
    <string name="people_tile_description">See recent messages, missed calls, and status updates</string>
    <!-- Title text displayed for the Conversation widget [CHAR LIMIT=50] -->
    <string name="people_tile_title">Conversation</string>
    <!-- Text when the Conversation widget when Do Not Disturb is suppressing the notification. [CHAR LIMIT=50] -->
    <string name="paused_by_dnd">Paused by Do Not Disturb</string>
    <!-- Content description text on the Conversation widget when a person has sent a new text message [CHAR LIMIT=150] -->
    <string name="new_notification_text_content_description"><xliff:g id="name" example="Anna">%1$s</xliff:g> sent a message: <xliff:g id="notification" example="Hey! How is your day going">%2$s</xliff:g></string>
    <!-- Content description text on the Conversation widget when a person has sent a new image message [CHAR LIMIT=150] -->
    <string name="new_notification_image_content_description"><xliff:g id="name" example="Anna">%1$s</xliff:g> sent an image</string>
    <!-- Content description text on the Conversation widget when a person has a new status posted [CHAR LIMIT=150] -->
    <string name="new_status_content_description"><xliff:g id="name" example="Anna">%1$s</xliff:g> has a status update: <xliff:g id="status" example="Listening to music">%2$s</xliff:g></string>
    <!-- Content description text on the Conversation widget when a person is  available, meaning online on an application [CHAR LIMIT=150] -->
    <string name="person_available">Available</string>

    <!-- Title to display in a notification when ACTION_BATTERY_CHANGED.EXTRA_PRESENT field is false
    [CHAR LIMIT=NONE] -->
    <string name="battery_state_unknown_notification_title">Problem reading your battery meter</string>
    <!-- Text to display in a notification when ACTION_BATTERY_CHANGED.EXTRA_PRESENT field is false
    [CHAR LIMIT=NONE] -->
    <string name="battery_state_unknown_notification_text">Tap for more information</string>

    <!-- Secondary label for alarm tile when there is no next alarm information [CHAR LIMIT=20] -->
    <string name="qs_alarm_tile_no_alarm">No alarm set</string>

    <!-- Accessibility label for a11y action to show the bouncer (pin/pattern/password) screen lock [CHAR LIMIT=NONE] -->
    <string name="accessibility_bouncer">enter screen lock</string>
    <!-- Accessibility label for side fingerprint sensor indicator [CHAR LIMIT=NONE] -->
    <string name="accessibility_side_fingerprint_indicator_label">Touch the fingerprint sensor. It\u2019s the shorter button on the side of the phone</string>
    <!-- Accessibility label for fingerprint sensor [CHAR LIMIT=NONE] -->
    <string name="accessibility_fingerprint_label">Fingerprint sensor</string>
    <!-- Accessibility action for tapping on an affordance that will bring up the user's
    pin/pattern/password bouncer (ie: "Double tap to authenticate") [CHAR LIMIT=NONE] -->
    <string name="accessibility_authenticate_hint">authenticate</string>
    <!-- Accessibility action for tapping on an affordance on an unlocked lock screen (ie: "Double
     tap to enter device") [CHAR LIMIT=NONE] -->
    <string name="accessibility_enter_hint">enter device</string>
    <!-- Message shown to suggest authentication using fingerprint [CHAR LIMIT=60]-->
    <string name="keyguard_try_fingerprint">Use fingerprint to open</string>
    <!-- Accessibility announcement to inform user to unlock using the fingerprint sensor [CHAR LIMIT=NONE] -->
    <string name="accessibility_fingerprint_bouncer">Authentication required. Touch the fingerprint sensor to authenticate.</string>

    <!-- Content description for a chip in the status bar showing that the user is currently on a phone call. [CHAR LIMIT=NONE] -->
    <string name="ongoing_phone_call_content_description">Ongoing phone call</string>

    <!-- Provider Model: Default title of the mobile network in the mobile layout. [CHAR LIMIT=50] -->
    <string name="mobile_data_settings_title">Mobile data</string>
    <!-- Provider Model: Summary text separator for preferences including a short description
         (eg. "Connected / 5G"). [CHAR LIMIT=50] -->
    <string name="preference_summary_default_combination"><xliff:g id="state" example="Connected">%1$s</xliff:g> / <xliff:g id="networkMode" example="LTE">%2$s</xliff:g></string>
    <!-- Provider Model:
         Summary indicating that a SIM has an active mobile data connection [CHAR LIMIT=50] -->
    <string name="mobile_data_connection_active">Connected</string>
    <!-- Provider Model:
         Summary indicating that a SIM is temporarily connected to mobile data [CHAR LIMIT=50] -->
    <string name="mobile_data_temp_connection_active">Temporarily connected</string>
    <!-- Provider Model:
     Summary indicating that a SIM is temporarily connected to mobile data [CHAR LIMIT=50] -->
    <string name="mobile_data_poor_connection">Poor connection</string>
    <!-- Provider Model:
     Summary indicating that a SIM has no mobile data connection [CHAR LIMIT=50] -->
    <string name="mobile_data_off_summary">Mobile data won\u0027t auto\u2011connect</string>
    <!-- Provider Model:
     Summary indicating that a active SIM and no network available [CHAR LIMIT=50] -->
    <string name="mobile_data_no_connection">No connection</string>
    <!-- Provider Model: Summary indicating that no other networks available [CHAR LIMIT=50] -->
    <string name="non_carrier_network_unavailable">No other networks available</string>
    <!-- Provider Model: Summary indicating that no networks available [CHAR LIMIT=50] -->
    <string name="all_network_unavailable">No networks available</string>
    <!-- Provider Model: Panel title text for turning on the Wi-Fi networks. [CHAR LIMIT=40] -->
    <string name="turn_on_wifi">Wi\u2011Fi</string>
    <!-- Provider Model: Panel subtitle for tapping a network to connect to internet. [CHAR LIMIT=60] -->
    <string name="tap_a_network_to_connect">Tap a network to connect</string>
    <!-- Provider Model: Panel subtitle for unlocking screen to view networks. [CHAR LIMIT=60] -->
    <string name="unlock_to_view_networks">Unlock to view networks</string>
    <!-- Provider Model: Wi-Fi settings. text displayed when Wi-Fi is on and network list is empty [CHAR LIMIT=50]-->
    <string name="wifi_empty_list_wifi_on">Searching for networks\u2026</string>
    <!-- Provider Model: Failure notification for connect -->
    <string name="wifi_failed_connect_message">Failed to connect to network</string>
    <!-- Provider Model: Toast message for when the user selects cellular as the internet provider and Wi-Fi auto-connect is temporarily disabled [CHAR LIMIT=60]-->
    <string name="wifi_wont_autoconnect_for_now">Wi\u2011Fi won\u2019t auto-connect for now</string>
    <!-- Provider Model: Title to see all the networks [CHAR LIMIT=50] -->
    <string name="see_all_networks">See all</string>
    <!-- Summary for warning to disconnect ethernet first then switch to other networks. [CHAR LIMIT=60] -->
    <string name="to_switch_networks_disconnect_ethernet">To switch networks, disconnect ethernet</string>
    <!-- Message to describe "Wi-Fi scan always available feature" when Wi-Fi is off and Wi-Fi scanning is on. [CHAR LIMIT=NONE] -->
    <string name="wifi_scan_notify_message">To improve device experience, apps and services can still scan for Wi\u2011Fi networks at any time, even when Wi\u2011Fi is off. You can change this in Wi\u2011Fi scanning settings. <annotation id="link">Change</annotation></string>
    <!-- Provider Model: Description of the airplane mode button. [CHAR LIMIT=60] -->
    <string name="turn_off_airplane_mode">Turn off airplane mode</string>

    <!-- Text for TileService request dialog. This is shown to the user that an app is requesting
         user approval to add the shown tile to Quick Settings [CHAR LIMIT=NONE] -->
    <string name="qs_tile_request_dialog_text"><xliff:g id="appName" example="Fake App">%1$s</xliff:g> wants to add the following tile to Quick Settings</string>
    <!-- Text for TileService request dialog. Text for button for user to approve adding the tile [CHAR LIMIT=20] -->
    <string name="qs_tile_request_dialog_add">Add tile</string>
    <!-- Text for TileService request dialog. Text for button for user to reject adding the tile [CHAR LIMIT=20] -->
    <string name="qs_tile_request_dialog_not_add">Do not add tile</string>

    <!-- Title for User Switch dialog. [CHAR LIMIT=20] -->
    <string name="qs_user_switch_dialog_title">Select user</string>

    <!-- Label for the entry point to open the dialog which shows currently running applications [CHAR LIMIT=NONE]-->
    <string name="fgs_manager_footer_label">{count, plural,
        =1 {# app is active}
        other {# apps are active}
    }</string>
    <!-- Content description for a dot indicator in the running application indicating that there
    is new information [CHAR LIMIT=NONE] -->
    <string name="fgs_dot_content_description">New information</string>
    <!-- Title for dialog listing applications currently running [CHAR LIMIT=NONE]-->
    <string name="fgs_manager_dialog_title">Active apps</string>
    <!-- Detailed message for dialog listing applications currently running [CHAR LIMIT=NONE]-->
    <string name="fgs_manager_dialog_message">These apps are active and running, even when you\u2019re not using them. This improves their functionality, but it may also affect battery life.</string>
    <!-- Label of the button to stop an app from running [CHAR LIMIT=12]-->
    <string name="fgs_manager_app_item_stop_button_label">Stop</string>
    <!-- Label of the button to stop an app from running but the app is already stopped and the button is disabled [CHAR LIMIT=12]-->
    <string name="fgs_manager_app_item_stop_button_stopped_label">Stopped</string>

    <!-- Label for button to finish and copy edited text back to the clipboard [CHAR LIMIT=20] -->
    <string name="clipboard_edit_text_done">Done</string>
    <!-- Text informing user that content has been copied to the system clipboard [CHAR LIMIT=NONE] -->
    <string name="clipboard_overlay_text_copied">Copied</string>
    <!-- Text informing user where text being edited was copied from [CHAR LIMIT=NONE] -->
    <string name="clipboard_edit_source">From <xliff:g id="appName" example="Gmail">%1$s</xliff:g></string>
    <!-- Label for button to dismiss clipboard overlay [CHAR LIMIT=NONE] -->
    <string name="clipboard_dismiss_description">Dismiss copied text</string>
    <!-- Label for button to edit text that was copied to the clipboard [CHAR LIMIT=NONE] -->
    <string name="clipboard_edit_text_description">Edit copied text</string>
    <!-- Label for button to edit an image that was copied to the clipboard [CHAR LIMIT=NONE] -->
    <string name="clipboard_edit_image_description">Edit copied image</string>
    <!-- Label for button to send copied content to a nearby device [CHAR LIMIT=NONE] -->
    <string name="clipboard_send_nearby_description">Send to nearby device</string>
    <!-- Text informing user that copied content is hidden [CHAR LIMIT=NONE] -->
    <string name="clipboard_text_hidden">Tap to view</string>
    <!-- Asterisks replacing sensitive text content -->
    <string name="clipboard_asterisks" translatable="false">••••••</string>
    <!-- Accessibility announcement informing user that text has been copied [CHAR LIMIT=NONE] -->
    <string name="clipboard_text_copied">Text copied</string>
    <!-- Accessibility announcement informing user that text has been copied [CHAR LIMIT=NONE] -->
    <string name="clipboard_image_copied">Image copied</string>
    <!-- Accessibility announcement informing user that something has been copied [CHAR LIMIT=NONE] -->
    <string name="clipboard_content_copied">Content copied</string>
    <!-- Name of the screen that lets the user edit the context of the clipboard (copy/paste) [CHAR LIMIT=NONE] -->
    <string name="clipboard_editor">Clipboard Editor</string>
    <!-- Name for the window showing the clipboard (copy/paste) preview and controls [CHAR LIMIT=NONE] -->
    <string name="clipboard_overlay_window_name">Clipboard</string>
    <!-- Accessibility label for an image preview [CHAR LIMIT=NONE] -->
    <string name="clipboard_image_preview">Image preview</string>
    <!-- Accessibility string describing what will happen when the user selects the clipboard preview. Completing the sentence "Double tap to ..." [CHAR LIMIT=NONE] -->
    <string name="clipboard_edit">edit</string>

    <!-- Generic "add" string [CHAR LIMIT=NONE] -->
    <string name="add">Add</string>
    <!-- Add supervised user -->
    <string name="add_user_supervised" translatable="false">@*android:string/supervised_user_creation_label</string>
    <!-- Manage users - For system user management [CHAR LIMIT=40]  -->
    <string name="manage_users">Manage users</string>

    <!-- Toast shown when a notification does not support dragging to split [CHAR LIMIT=NONE] -->
    <string name="drag_split_not_supported">This notification does not support dragging to split screen</string>

    <!-- Content description for the Wi-Fi off icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_wifi_off">Wi\u2011Fi unavailable</string>
    <!-- Content description for the priority mode icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_priority_mode">Priority mode</string>
    <!-- Content description for the alarm set icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_alarm_set">Alarm set</string>
    <!-- Content description for the camera off icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_camera_off">Camera is off</string>
    <!-- Content description for the mic off icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_mic_off">Mic is off</string>
    <!-- Content description for the camera and mic off icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_camera_mic_off">Camera and mic are off</string>
    <!-- Content description for the notifications indicator icon in the dream overlay status bar [CHAR LIMIT=NONE] -->
    <string name="dream_overlay_status_bar_notification_indicator">{count, plural,
    =1 {# notification}
    other {# notifications}
    }</string>
    <!-- Accessibility label for weather complication on dreams with weather condition and temperature [CHAR_LIMIT=200] -->
    <string name="dream_overlay_weather_complication_desc">
        <xliff:g id="weather_condition" example="Partly cloudy">%1$s</xliff:g>, <xliff:g id="temperature" example="7°C">%2$s</xliff:g>
    </string>

    <!-- [CHAR LIMIT=30] Label used to open Note Task -->
    <string name="note_task_button_label">Note-taking</string>

    <!-- [CHAR LIMIT=25] Long label used by Note Task Shortcut -->
    <string name="note_task_shortcut_long_label">Note-taking, <xliff:g id="note_taking_app" example="Note-taking App">%1$s</xliff:g></string>

    <!-- [CHAR LIMIT=NONE] Output switch chip text during broadcasting -->
    <string name="audio_sharing_description">Sharing audio</string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, media app is broadcasting -->
    <string name="broadcasting_description_is_broadcasting">Broadcasting</string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, title -->
    <string name="bt_le_audio_broadcast_dialog_title">Stop broadcasting <xliff:g id="app_name" example="App Name 1">%1$s</xliff:g>?</string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, sub-title -->
    <string name="bt_le_audio_broadcast_dialog_sub_title">If you broadcast <xliff:g id="switchApp" example="App Name 2">%1$s</xliff:g> or change the output, your current broadcast will stop</string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, switch to others app. -->
    <string name="bt_le_audio_broadcast_dialog_switch_app">Broadcast <xliff:g id="switchApp" example="App Name 2">%1$s</xliff:g></string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, different output. -->
    <string name="bt_le_audio_broadcast_dialog_different_output">Change output</string>
    <!-- [CHAR LIMIT=NONE] Le audio broadcast dialog, media app is unknown -->
    <string name="bt_le_audio_broadcast_dialog_unknown_name">Unknown</string>

    <!-- Time format for the Dream Time Complication for 12-hour time format [CHAR LIMIT=NONE] -->
    <string name="dream_time_complication_12_hr_time_format">h:mm</string>

    <!-- Time format for the Dream Time Complication for 24-hour time format [CHAR LIMIT=NONE] -->
    <string name="dream_time_complication_24_hr_time_format">kk:mm</string>

    <!-- Title for the log access confirmation dialog. [CHAR LIMIT=NONE] -->
    <string name="log_access_confirmation_title">Allow <xliff:g id="log_access_app_name" example="Example App">%s</xliff:g> to access all device logs?</string>
    <!-- Label for the allow button on the log access confirmation dialog. [CHAR LIMIT=40] -->
    <string name="log_access_confirmation_allow">Allow one-time access</string>
    <!-- Label for the deny button on the log access confirmation dialog. [CHAR LIMIT=20] -->
    <string name="log_access_confirmation_deny">Don\u2019t allow</string>

    <!-- Content for the log access confirmation dialog. [CHAR LIMIT=NONE]-->
    <string name="log_access_confirmation_body">Device logs record what happens on your device. Apps can use these logs to find and fix issues.\n\nSome logs may contain sensitive info, so only allow apps you trust to access all device logs.
        \n\nIf you don’t allow this app to access all device logs, it can still access its own logs. Your device manufacturer may still be able to access some logs or info on your device.
    </string>

    <!-- Learn more URL for the log access confirmation dialog. [DO NOT TRANSLATE]-->
    <string name="log_access_confirmation_learn_more_url" translatable="false">https://support.google.com/android/answer/12986432</string>
    <string name="log_access_confirmation_learn_more">Learn more</string>
    <string name="log_access_confirmation_learn_more_at">Learn more at <xliff:g id="url" example="g.co/android/devicelogs">%s</xliff:g></string>

    <!--
    Template for an action that opens a specific app. [CHAR LIMIT=16]
    -->
    <string name="keyguard_affordance_enablement_dialog_action_template">Open <xliff:g id="appName" example="Wallet">%1$s</xliff:g></string>

    <!---
    Explains that the wallet app is not available because it is not installed. This is shown as part
    of a dialog that explains to the user why they cannot select this shortcut for their lock screen
    right now.
    [CHAR LIMIT=NONE].
    -->
    <string name="wallet_quick_affordance_unavailable_install_the_app">To add the Wallet app as a shortcut, make sure the app is installed</string>

    <!---
    Explains that the wallet app is not available because it is not installed. This is shown as part
    of a dialog that explains to the user why they cannot select this shortcut for their lock screen
    right now.
    [CHAR LIMIT=NONE].
    -->
    <string name="wallet_quick_affordance_unavailable_configure_the_app">To add the Wallet app as a shortcut, make sure at least one card has been added</string>

    <!--
    Explains that the lock screen shortcut for the "home" app is not available because the app isn't
    installed. This is shown as part of a dialog that explains to the user why they cannot select
    this shortcut for their lock screen right now. [CHAR LIMIT=NONE].
    -->
    <string name="home_quick_affordance_unavailable_install_the_app">To add the Home app as a shortcut, make sure the app is installed</string>

    <!--
    Explains that the lock screen shortcut for the "home" app is not available because the app isn't
    configured. This is shown as part of a dialog that explains to the user why they cannot select
    this shortcut for their lock screen right now. [CHAR LIMIT=NONE].
    -->
    <string name="home_quick_affordance_unavailable_configure_the_app">&#8226; At least one device or device panel are available</string>

    <!---
    Explains that the notes app is not available. This is shown as part of a dialog that explains to
    the user why they cannot select this shortcut for their lock screen right now.
    [CHAR LIMIT=NONE].
    -->
    <string name="notes_app_quick_affordance_unavailable_explanation">Select a default notes app to use the notetaking shortcut</string>

    <!---
    The action to make the lock screen shortcut for the notes app to be available for the user to
    use. This is shown as the action button in the dialog explaining why the shortcut isn't
    available. [CHAR LIMIT=NONE] -->
    <string name="keyguard_affordance_enablement_dialog_notes_app_action">Select app</string>

    <!--
    Error message shown when a shortcut must be pressed and held to activate it, usually shown when
    the user tried to tap the shortcut or held it for too short a time. [CHAR LIMIT=32].
    -->
    <string name="keyguard_affordance_press_too_short">Touch &amp; hold shortcut</string>

    <!-- Text for education page of cancel button to hide the page. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_bottom_sheet_cancel">Cancel</string>
    <!-- Text for the user to confirm they flipped the device around. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_bottom_sheet_confirm">Switch screens now</string>
    <!-- Text for education page title to guide user to unfold phone. [CHAR_LIMIT=50] -->
    <string name="rear_display_folded_bottom_sheet_title">Unfold phone</string>
    <!-- Text for education page title to guide user to switch to the front display. [CHAR_LIMIT=50] -->
    <string name="rear_display_unfolded_bottom_sheet_title">Switch screens?</string>
    <!-- Text for education page description to suggest user to use rear selfie capture. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_folded_bottom_sheet_description">For higher resolution, use the rear camera</string>
    <!-- Text for unfolded education page description to suggest user to use rear selfie capture. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_unfolded_bottom_sheet_description">For higher resolution, flip the phone</string>
    <!-- Text for education page content description for folded animation. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_accessibility_folded_animation">Foldable device being unfolded</string>
    <!-- Text for education page content description for unfolded animation. [CHAR_LIMIT=NONE] -->
    <string name="rear_display_accessibility_unfolded_animation">Foldable device being flipped around</string>

    <!-- QuickSettings: Additional label for the auto-rotation quicksettings tile indicating that the setting corresponds to the folded posture for a foldable device [CHAR LIMIT=32] -->
    <string name="quick_settings_rotation_posture_folded">folded</string>
    <!-- QuickSettings: Additional label for the auto-rotation quicksettings tile indicating that the setting corresponds to the unfolded posture for a foldable device [CHAR LIMIT=32] -->
    <string name="quick_settings_rotation_posture_unfolded">unfolded</string>
    <!-- QuickSettings: template for rotation tile foldable secondary label [CHAR LIMIT=64] !-->
    <string name="rotation_tile_with_posture_secondary_label_template">%1$s / %2$s</string>
    <!-- Title for notification of low stylus battery with percentage. "percentage" is
        the value of the battery capacity remaining [CHAR LIMIT=none]-->
    <string name="stylus_battery_low_percentage">Stylus battery <xliff:g id="percentage" example="16%">%s</xliff:g></string>
    <!-- Subtitle for the notification sent when a stylus battery is low. [CHAR LIMIT=none]-->
    <string name="stylus_battery_low_subtitle">Connect your stylus to a charger</string>

    <!-- Title for notification of low stylus battery. [CHAR_LIMIT=NONE] -->
    <string name="stylus_battery_low">Stylus battery low</string>

    <!-- Label for a lock screen shortcut to start the camera in video mode. [CHAR_LIMIT=16] -->
    <string name="video_camera">Video camera</string>

    <!-- Switch to work profile dialer app for placing a call dialog. -->
    <!-- Text for Switch to work profile dialog's Title. Switch to work profile dialog guide users to make call from work
    profile dialer app as it's not possible to make call from current profile due to an admin policy. [CHAR LIMIT=60] -->
    <string name="call_from_work_profile_title">Can\'t call from a personal app</string>
    <!-- Text for switch to work profile for call dialog to guide users to make call from work
    profile dialer app as it's not possible to make call from current profile due to an admin policy. [CHAR LIMIT=NONE]
    -->
    <string name="call_from_work_profile_text">Your organization only allows you to make calls from work apps</string>
    <!-- Label for the button to switch to work profile for placing call. Switch to work profile dialog guide users to make call from work
    profile dialer app as it's not possible to make call from current profile due to an admin policy.[CHAR LIMIT=60] -->
    <string name="call_from_work_profile_action">Switch to work profile</string>
    <!-- Label for the button to switch to work profile for placing call. Switch to work profile dialog guide users to make call from work
    profile dialer app as it's not possible to make call from current profile due to an admin policy.[CHAR LIMIT=60] -->
    <string name="install_dialer_on_work_profile_action">Install a work phone app</string>
    <!-- Label for the close button on switch to work profile dialog. Switch to work profile dialog guide users to make call from work
    profile dialer app as it's not possible to make call from current profile due to an admin policy.[CHAR LIMIT=60] -->
    <string name="call_from_work_profile_close">Cancel</string>

    <!--
    Label for a menu item in a menu that is shown when the user wishes to customize the lock screen.
    Clicking on this menu item takes the user to a screen where they can modify the settings of the
    lock screen.

    It is critical that this text is as short as possible. If needed, translators should feel free
    to drop "lock screen" from their translation and keep just "Customize" or "Customization", in
    cases when that verb is not available in their target language.

    [CHAR LIMIT=32]
    -->
    <string name="lock_screen_settings">Customize lock screen</string>

    <!-- Title of security view when we want to authenticate before customizing the lockscreen. [CHAR LIMIT=NONE] -->
    <string name="keyguard_unlock_to_customize_ls">Unlock to customize lock screen</string>

    <!-- Content description for Wi-Fi not available icon on dream [CHAR LIMIT=NONE]-->
    <string name="wifi_unavailable_dream_overlay_content_description">Wi-Fi not available</string>

    <!-- Content description for camera blocked icon on dream [CHAR LIMIT=NONE] -->
    <string name="camera_blocked_dream_overlay_content_description">Camera blocked</string>

    <!-- Content description for camera and microphone blocked icon on dream [CHAR LIMIT=NONE] -->
    <string name="camera_and_microphone_blocked_dream_overlay_content_description">Camera and microphone blocked</string>

    <!-- Content description for camera and microphone disabled icon on dream [CHAR LIMIT=NONE] -->
    <string name="microphone_blocked_dream_overlay_content_description">Microphone blocked</string>

    <!-- Content description for priority mode icon on dream [CHAR LIMIT=NONE] -->
    <string name="priority_mode_dream_overlay_content_description">Do not disturb</string>

    <!-- Content description for when assistant attention is active [CHAR LIMIT=NONE] -->
    <string name="assistant_attention_content_description">User presence is detected</string>

    <!--- Content of toast triggered when the notes app entry point is triggered without setting a default notes app. [CHAR LIMIT=NONE] -->
    <string name="set_default_notes_app_toast_content">Set default notes app in Settings</string>

    <!-- Label for a button that, when clicked, sends the user to the app store to install an app. [CHAR LIMIT=64]. -->
    <string name="install_app">Install app</string>

    <!-- Instructions informing the user they can swipe up on the lockscreen to dismiss [CHAR LIMIT=48]-->
    <string name="dismissible_keyguard_swipe">Swipe up to continue</string>

    <!--- Title of the dialog appearing when an external display is connected, asking whether to start mirroring [CHAR LIMIT=NONE]-->
    <string name="connected_display_dialog_start_mirroring">Mirror to external display?</string>
    <!--- Body of the mirroring dialog, shown when dual display is enabled. This signals that enabling mirroring will stop concurrent displays on a foldable device. [CHAR LIMIT=NONE]-->
    <string name="connected_display_dialog_dual_display_stop_warning">Your inner display will be mirrored. Your front display will be turned off.</string>
    <!--- Label of the "enable display" button of the dialog appearing when an external display is connected [CHAR LIMIT=NONE]-->
    <string name="mirror_display">Mirror display</string>
    <!--- Label of the dismiss button of the dialog appearing when an external display is connected [CHAR LIMIT=NONE]-->
    <string name="dismiss_dialog">Dismiss</string>

    <!--- Content description of the connected display status bar icon that appears every time a display is connected [CHAR LIMIT=NONE]-->
    <string name="connected_display_icon_desc">Display connected</string>

    <!-- Title of the privacy dialog, shown for active / recent app usage of some phone sensors [CHAR LIMIT=30] -->
    <string name="privacy_dialog_title">Microphone &amp; Camera</string>
    <!-- Subtitle of the privacy dialog, shown for active / recent app usage of some phone sensors [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_summary">Recent app use</string>
    <!-- Label of the secondary button of the privacy dialog, used to check recent app usage of phone sensors [CHAR LIMIT=30] -->
    <string name="privacy_dialog_more_button">See recent access</string>
    <!-- Label of the primary button to dismiss the privacy dialog [CHAR LIMIT=20] -->
    <string name="privacy_dialog_done_button">Done</string>
    <!-- Description for expanding a collapsible widget in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_expand_action">Expand and show options</string>
    <!-- Description for collapsing a collapsible widget in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_collapse_action">Collapse</string>
    <!-- Label of a button of the privacy dialog to close an app actively using a phone sensor [CHAR LIMIT=50] -->
    <string name="privacy_dialog_close_app_button">Close this app</string>
    <!-- Message shown in the privacy dialog when an app actively using a phone sensor is closed [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_close_app_message"><xliff:g id="app_name" example="Gmail">%1$s</xliff:g> closed</string>
    <!-- Label of a button of the privacy dialog to learn more of a service actively or recently using a phone sensor [CHAR LIMIT=50] -->
    <string name="privacy_dialog_manage_service">Manage service</string>
    <!-- Label of a button of the privacy dialog to manage permissions of an app actively or recently using a phone sensor [CHAR LIMIT=50] -->
    <string name="privacy_dialog_manage_permissions">Manage access</string>
    <!-- Label for active usage of a phone sensor by phone call in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_active_call_usage">In use by phone call</string>
    <!-- Label for recent usage of a phone sensor by phone call in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_recent_call_usage">Recently used in phone call</string>
    <!-- Label for active app usage of a phone sensor in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_active_app_usage">In use by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g></string>
    <!-- Label for recent app usage of a phone sensor in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_recent_app_usage">Recently used by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g></string>
    <!-- Label for active app usage of a phone sensor with sub-attribution or proxy label in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_active_app_usage_1">In use by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g> (<xliff:g id="attribution_label" example="For Wallet">%2$s</xliff:g>)</string>
    <!-- Label for recent app usage of a phone sensor with sub-attribution or proxy label in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_recent_app_usage_1">Recently used by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g> (<xliff:g id="attribution_label" example="For Wallet">%2$s</xliff:g>)</string>
    <!-- Label for active app usage of a phone sensor with sub-attribution and proxy label in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_active_app_usage_2">In use by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g> (<xliff:g id="attribution_label" example="For Wallet">%2$s</xliff:g> \u2022 <xliff:g id="proxy_label" example="Speech services">%3$s</xliff:g>)</string>
    <!-- Label for recent app usage of a phone sensor with sub-attribution and proxy label in the privacy dialog [CHAR LIMIT=NONE] -->
    <string name="privacy_dialog_recent_app_usage_2">Recently used by <xliff:g id="app_name" example="Gmail">%1$s</xliff:g> (<xliff:g id="attribution_label" example="For Wallet">%2$s</xliff:g> \u2022 <xliff:g id="proxy_label" example="Speech services">%3$s</xliff:g>)</string>

    <!-- Title of the keyboard shortcut helper category "System". The helper is a component that
         shows the user which keyboard shortcuts they can use. The "System" shortcuts are for
         example "Take a screenshot" or "Go back". [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_category_system">System</string>
    <!-- Title of the keyboard shortcut helper category "Multitasking". The helper is a component
         that shows the user which keyboard shortcuts they can use. The "Multitasking" shortcuts are
         for example "Enter split screen". [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_category_multitasking">Multitasking</string>
    <!-- Title of the keyboard shortcut helper category "Input". The helper is a component
         that shows the user which keyboard shortcuts they can use. The "Input" shortcuts are
         the ones provided by the keyboard. Examples are "Access emoji" or "Switch to next language"
         [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_category_input">Input</string>
    <!-- Title of the keyboard shortcut helper category "App shortcuts". The helper is a component
         that shows the user which keyboard shortcuts they can use. The "App shortcuts" are
         for example "Open browser" or "Open calculator". [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_category_app_shortcuts">App shortcuts</string>
    <!-- Title of the keyboard shortcut helper category "Accessibility". The helper is a component
         that shows the user which keyboard shortcuts they can use. The "Accessibility" shortcuts
         are for example "Turn on talkback". [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_category_a11y">Accessibility</string>
    <!-- Title at the top of the keyboard shortcut helper UI. The helper is a component
         that shows the user which keyboard shortcuts they can use. [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_title">Keyboard shortcuts</string>
    <!-- Placeholder text shown in the search box of the keyboard shortcut helper, when the user
         hasn't typed in anything in the search box yet. The helper is a  component that shows the
         user which keyboard shortcuts they can use. [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_search_placeholder">Search shortcuts</string>
    <!-- Content description of the icon that allows to collapse a keyboard shortcut helper category
         panel. The helper is a  component that shows the  user which keyboard shortcuts they can
         use. The helper shows shortcuts in categories, which can be collapsed or expanded.
         [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_content_description_collapse_icon">Collapse icon</string>
    <!-- Content description of the icon that allows to expand a keyboard shortcut helper category
         panel. The helper is a  component that shows the  user which keyboard shortcuts they can
         use. The helper shows shortcuts in categories, which can be collapsed or expanded.
         [CHAR LIMIT=NONE] -->
    <string name="shortcut_helper_content_description_expand_icon">Expand icon</string>
    <!-- Word that separates different possible key combinations of a shortcut. For example the
         "Go to home screen" shortcut could be triggered using "home button" OR "ctrl + h".
         This is that "or" separator.
         The keyboard shortcut helper is a  component that shows the  user which keyboard shortcuts
         they can use.
         [CHAR LIMIT=NONE]
          -->
    <string name="shortcut_helper_key_combinations_or_separator">or</string>

    <!-- Label for button opening tutorial for back gesture on touchpad [CHAR LIMIT=NONE] -->
    <string name="touchpad_tutorial_back_gesture_button">Back gesture</string>
    <!-- Label for button opening tutorial for back gesture on touchpad [CHAR LIMIT=NONE] -->
    <string name="touchpad_tutorial_home_gesture_button">Home gesture</string>
    <!-- Label for button opening tutorial on using Action key from keyboard [CHAR LIMIT=NONE] -->
    <string name="touchpad_tutorial_action_key_button">Action key</string>
    <!-- Label for button finishing touchpad tutorial [CHAR LIMIT=NONE] -->
    <string name="touchpad_tutorial_done_button">Done</string>

    <!-- Content description for keyboard backlight brightness dialog [CHAR LIMIT=NONE] -->
    <string name="keyboard_backlight_dialog_title">Keyboard backlight</string>
    <!-- Content description for keyboard backlight brightness value [CHAR LIMIT=NONE] -->
    <string name="keyboard_backlight_value">Level %1$d of %2$d</string>
    <!-- Label for home control panel [CHAR LIMIT=30] -->
    <string name="home_controls_dream_label">Home Controls</string>
    <!-- Description for home control panel [CHAR LIMIT=67] -->
    <string name="home_controls_dream_description">Quickly access your home controls as a screensaver</string>
    <!--redmine 297448 add for battery temperature abnormal alarm by zhouzihan 2024.11.07 begin-->
    <string name="temperaure_high_dialog">Temperature is high. The device needs to cool down before you can use or charge it.</string>
    <string name="temperaure_high_stop_charge">Temperature is high. Charging stopped. Please remove the charger.</string>
    <string name="temperaure_high_dialog_power_off">Temperature is too high. Overheated app is being closed. Please use the device after it cools down, or it will turn off automatically.</string>
    <string name="temperaure_low_stop_charge">Please wait until the battery warms up.</string>
    <string name="temperaure_low_dialog">Battery low temperature, don\'t charge.</string>
    <string name="temperaure_low_dialog_power_off">Battery temperature too low, the phone will soon power off.</string>
    <string name="ok">OK</string>
    <!--redmine 297448 add for battery temperature abnormal alarm by zhouzihan 2024.11.07 end-->
    <!-- SR-MMI:297459 modify for name by guangweiwei begin -->
    <!-- Do not translate. Default access point SSID used for tethering -->
    <string name="wifi_tether_configure_ssid_default" translatable="false">SMART 12</string>
    <!-- SR-MMI:297459 modify for name by guangweiwei end -->
    <!--Redmine 306810Add custom usb dialog title by fuhekai 2024.12.18 start-->
    <string name="sr_usb_dialog_title">Phone is charging. Would you like to transfer files?</string>
    <string name="bottom_sheet_dialog_charging_more">More</string>
    <string name="bottom_sheet_dialog_charging_title">USB use for</string>
    <string name="bottom_sheet_dialog_charging_only">Charging only</string>
    <string name="bottom_sheet_dialog_charging_picture">Picture Transfer</string>
    <string name="bottom_sheet_dialog_charging_file">File transfers</string>
    <string name="sr_usb_counter">(<xliff:g id="sr_usb_time">%1$d</xliff:g><xliff:g id="sr_usb_unit">%2$s</xliff:g>)</string>
    <string name="sr_usb_unit"> s</string>
    <!--Redmine 306810 Add custom usb dialog title by fuhekai 2024.12.18 end-->
    <!--redmine 317476 modify After inserting two SIM cards, a SIM card selection menu will pop up by sdy begin -->
    <string name="sr_sim_select_dialog_title">Choose SIM for mobile data</string>
    <string name="sr_set_data_card_remind">Switching SIM card for connecting to data network, which may take one minute...</string>
    <!--redmine 317476 modify After inserting two SIM cards, a SIM card selection menu will pop up by sdy end -->
    <!--Redmine 322354 ringtone button develop by zzm 20250314 begin -->
    <string name="quick_settings_ring_label">Ringtone</string>
    <string name="quick_settings_ring_normal">Ringtone</string>
    <string name="quick_settings_ring_vibrate">Vibrate</string>
    <string name="quick_settings_ring_silent">Mute</string>
    <!--Redmine 322354 ringtone button develop by zzm 20250314 end -->

    <!-- redmine-321738: add for highlight when FaceId by shaoxuesong 2025.0331@start -->
    <string name="sr_highlight_tips">Supplementary lighting for screen is being carried out to help complete face recognition.</string>
    <string name="sr_highlight_button_tips">OFF</string>
    <!-- redmine-321738: add for highlight when FaceId by shaoxuesong 2025.0331@start -->

    <!--redmine 305762 side key start-->
    <string name="quick_camera_start">start screen recording</string>
    <string name="mute_on">Muted</string>
    <string name="mute_off">Unmuted</string>
    <string name="do_not_disturb_on">Do Not Disturb on</string>
    <string name="do_not_disturb_off">Do Not Disturb off</string>
    <string name="quick_camera_stop">stop screen recording</string>
    <string name="quick_camera_dismiss">dismiss screen recording</string>
    <string name="no_flash_light">no flashlight</string>
    <string name="no_operation">FlexButton operation not set</string>
    <string name="in_ultra_power_save">Flexbutton is not available in ultra power saving mode</string>
    <!--redmine 312816 Fixed application to add corresponding prompts start-->
    <string name="screen_pinning_toast_side_key_pined">FlexButton is not supported in App Pinning</string>
    <!--redmine 312816 Fixed application to add corresponding prompts end-->
    <!--redmine 305762 side key end-->

    <!-- redmine 326916 modify ur status bar is ltr by hanmateng 20250414 begin-->
    <string name="force_ltr_languages">ur</string>
    <!-- redmine 326916 modify ur status bar is ltr by hanmateng 20250414 end-->
</resources>